# N8N 翻譯工作流程部署指南

## 📋 概述

本指南說明如何部署擴展後的 N8N 翻譯工作流程，包括安裝必要的依賴庫以支援 Word 和 Excel 檔案解析。

## 🔧 必要依賴庫

### Word 檔案處理
- **庫名**: `mammoth`
- **用途**: 解析 .docx 檔案並提取文本內容
- **官方文檔**: https://www.npmjs.com/package/mammoth

### Excel 檔案處理
- **庫名**: `xlsx`
- **用途**: 解析 .xlsx 檔案並提取表格數據
- **官方文檔**: https://www.npmjs.com/package/xlsx

## 🚀 部署方法

### 方法 1: Docker 環境部署

如果你使用 Docker 運行 N8N：

```bash
# 1. 進入 N8N 容器
docker exec -it your-n8n-container-name /bin/sh

# 2. 安裝依賴庫
npm install mammoth xlsx

# 3. 退出容器
exit

# 4. 重啟 N8N 容器
docker restart your-n8n-container-name
```

### 方法 2: 自託管環境部署

如果你直接在伺服器上運行 N8N：

```bash
# 1. 進入 N8N 安裝目錄
cd /path/to/your/n8n/installation

# 2. 安裝依賴庫
npm install mammoth xlsx

# 3. 重啟 N8N 服務
pm2 restart n8n
# 或者
systemctl restart n8n
```

### 方法 3: 使用 Docker Compose

如果你使用 Docker Compose，可以修改 `docker-compose.yml`：

```yaml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n
    # ... 其他配置
    volumes:
      - ./n8n-data:/home/<USER>/.n8n
      - ./package.json:/home/<USER>/package.json  # 掛載自定義 package.json
    command: >
      sh -c "npm install mammoth xlsx && n8n start"
```

## 📦 創建自定義 Docker 映像

為了確保依賴庫持久化，建議創建自定義 Docker 映像：

### 1. 創建 Dockerfile

```dockerfile
FROM n8nio/n8n:latest

USER root

# 安裝必要的依賴庫
RUN npm install -g mammoth xlsx

USER node

# 啟動 N8N
CMD ["n8n", "start"]
```

### 2. 建構映像

```bash
docker build -t n8n-with-libs .
```

### 3. 運行容器

```bash
docker run -d \
  --name n8n-translation \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  n8n-with-libs
```

## 🔍 驗證安裝

### 檢查庫是否正確安裝

在 N8N 的 Code 節點中運行以下代碼來驗證：

```javascript
// 檢查 mammoth 庫
try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 已安裝');
} catch (error) {
  console.log('❌ mammoth.js 未安裝:', error.message);
}

// 檢查 xlsx 庫
try {
  const XLSX = require('xlsx');
  console.log('✅ xlsx 已安裝');
} catch (error) {
  console.log('❌ xlsx 未安裝:', error.message);
}

return { status: 'Libraries checked' };
```

## 🎯 工作流程導入

### 1. 導入工作流程檔案

1. 登入 N8N 管理介面
2. 點擊 "Import from file" 或 "Import from URL"
3. 選擇 `fixed_workflow_complete_cleaned.json` 檔案
4. 確認導入成功

### 2. 配置憑證

確保以下 API 憑證已正確配置：

- **Google Drive OAuth2 API**: 用於檔案存取
- **Google Docs OAuth2 API**: 用於文檔創建
- **Gemini API Key**: 用於初步翻譯
- **Claude API Key**: 用於翻譯審校

### 3. 測試工作流程

1. 上傳測試檔案到指定的 Google Drive 資料夾
2. 檢查工作流程是否正確觸發
3. 驗證翻譯結果的品質和格式

## ⚠️ 故障排除

### 常見問題

1. **庫未找到錯誤**
   ```
   Error: Cannot find module 'mammoth'
   ```
   **解決方案**: 重新安裝庫並重啟 N8N

2. **權限問題**
   ```
   Permission denied
   ```
   **解決方案**: 確保以正確的用戶身份安裝庫

3. **記憶體不足**
   ```
   JavaScript heap out of memory
   ```
   **解決方案**: 增加 Node.js 記憶體限制
   ```bash
   export NODE_OPTIONS="--max-old-space-size=4096"
   ```

### 日誌檢查

```bash
# Docker 環境
docker logs your-n8n-container-name

# 系統服務
journalctl -u n8n -f
```

## 🔄 更新和維護

### 定期更新依賴庫

```bash
# 更新到最新版本
npm update mammoth xlsx

# 檢查過時的包
npm outdated
```

### 備份配置

定期備份 N8N 配置和工作流程：

```bash
# 備份 N8N 數據目錄
tar -czf n8n-backup-$(date +%Y%m%d).tar.gz /path/to/n8n/data
```

## 📞 支援

如果遇到部署問題，請檢查：

1. N8N 官方文檔: https://docs.n8n.io/
2. mammoth.js 文檔: https://github.com/mwilliamson/mammoth.js
3. xlsx 文檔: https://github.com/SheetJS/sheetjs

---

**注意**: 確保在生產環境中測試所有功能後再正式部署。
