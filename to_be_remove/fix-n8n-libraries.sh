#!/bin/bash

# N8N 庫修復腳本
# 此腳本幫助解決 N8N 無法載入 mammoth.js 和 xlsx 的問題

echo "🔧 N8N 庫修復腳本開始執行..."
echo "=================================="

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 檢查是否為 root 用戶
if [[ $EUID -eq 0 ]]; then
   echo -e "${YELLOW}警告: 正在以 root 用戶執行${NC}"
fi

# 1. 檢查 Node.js 和 npm
echo -e "\n${BLUE}1. 檢查 Node.js 環境${NC}"
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo "當前工作目錄: $(pwd)"

# 2. 檢查 N8N 進程
echo -e "\n${BLUE}2. 檢查 N8N 進程${NC}"
N8N_PID=$(pgrep -f "n8n")
if [ ! -z "$N8N_PID" ]; then
    echo -e "${GREEN}N8N 正在運行 (PID: $N8N_PID)${NC}"
    N8N_RUNNING=true
else
    echo -e "${YELLOW}N8N 未運行${NC}"
    N8N_RUNNING=false
fi

# 3. 尋找 N8N 安裝位置
echo -e "\n${BLUE}3. 尋找 N8N 安裝位置${NC}"
N8N_GLOBAL=$(npm list -g n8n 2>/dev/null | grep n8n | head -1)
if [ ! -z "$N8N_GLOBAL" ]; then
    echo -e "${GREEN}全域 N8N: $N8N_GLOBAL${NC}"
fi

N8N_PATH=$(which n8n 2>/dev/null)
if [ ! -z "$N8N_PATH" ]; then
    echo -e "${GREEN}N8N 執行檔: $N8N_PATH${NC}"
fi

# 4. 檢查現有庫安裝狀態
echo -e "\n${BLUE}4. 檢查現有庫安裝狀態${NC}"

# 檢查全域安裝
echo "檢查全域安裝:"
npm list -g mammoth 2>/dev/null && echo -e "${GREEN}✅ mammoth (全域)${NC}" || echo -e "${RED}❌ mammoth (全域)${NC}"
npm list -g xlsx 2>/dev/null && echo -e "${GREEN}✅ xlsx (全域)${NC}" || echo -e "${RED}❌ xlsx (全域)${NC}"

# 檢查本地安裝
echo "檢查本地安裝:"
npm list mammoth 2>/dev/null && echo -e "${GREEN}✅ mammoth (本地)${NC}" || echo -e "${RED}❌ mammoth (本地)${NC}"
npm list xlsx 2>/dev/null && echo -e "${GREEN}✅ xlsx (本地)${NC}" || echo -e "${RED}❌ xlsx (本地)${NC}"

# 5. 安裝/重新安裝庫
echo -e "\n${BLUE}5. 安裝/重新安裝庫${NC}"

# 停止 N8N（如果正在運行）
if [ "$N8N_RUNNING" = true ]; then
    echo -e "${YELLOW}停止 N8N 服務...${NC}"
    if command -v systemctl &> /dev/null; then
        sudo systemctl stop n8n 2>/dev/null
    elif command -v pm2 &> /dev/null; then
        pm2 stop n8n 2>/dev/null
    else
        kill $N8N_PID 2>/dev/null
    fi
    sleep 2
fi

# 安裝庫的多種方式
echo -e "${YELLOW}嘗試多種安裝方式...${NC}"

# 方式 1: 全域安裝
echo "方式 1: 全域安裝"
npm install -g mammoth xlsx
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 全域安裝成功${NC}"
else
    echo -e "${RED}❌ 全域安裝失敗${NC}"
fi

# 方式 2: 在當前目錄安裝
echo "方式 2: 在當前目錄安裝"
npm install mammoth xlsx
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 本地安裝成功${NC}"
else
    echo -e "${RED}❌ 本地安裝失敗${NC}"
fi

# 方式 3: 在 N8N 用戶目錄安裝（如果存在）
if [ -d "/home/<USER>" ]; then
    echo "方式 3: 在 N8N 用戶目錄安裝"
    cd /home/<USER>
    npm install mammoth xlsx
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ N8N 用戶目錄安裝成功${NC}"
    else
        echo -e "${RED}❌ N8N 用戶目錄安裝失敗${NC}"
    fi
fi

# 6. 設定環境變數
echo -e "\n${BLUE}6. 設定環境變數${NC}"
export NODE_PATH="/usr/local/lib/node_modules:/usr/lib/node_modules:$NODE_PATH"
echo "NODE_PATH 已設定為: $NODE_PATH"

# 7. 驗證安裝
echo -e "\n${BLUE}7. 驗證安裝${NC}"
node -e "
try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 載入成功');
} catch (e) {
  console.log('❌ mammoth.js 載入失敗:', e.message);
}

try {
  const XLSX = require('xlsx');
  console.log('✅ xlsx 載入成功');
} catch (e) {
  console.log('❌ xlsx 載入失敗:', e.message);
}
"

# 8. 重啟 N8N
echo -e "\n${BLUE}8. 重啟 N8N${NC}"
if [ "$N8N_RUNNING" = true ]; then
    echo -e "${YELLOW}重啟 N8N 服務...${NC}"
    if command -v systemctl &> /dev/null; then
        sudo systemctl start n8n
        sleep 3
        sudo systemctl status n8n
    elif command -v pm2 &> /dev/null; then
        pm2 start n8n
        pm2 status
    else
        echo -e "${YELLOW}請手動重啟 N8N 服務${NC}"
    fi
else
    echo -e "${YELLOW}請手動啟動 N8N 服務${NC}"
fi

# 9. 提供後續步驟
echo -e "\n${BLUE}9. 後續步驟${NC}"
echo "=================================="
echo "1. 確認 N8N 已重啟"
echo "2. 在 N8N 中創建一個新的 Code 節點"
echo "3. 運行診斷腳本 (n8n-debug-script.js)"
echo "4. 測試 Word 檔案處理工作流程"
echo ""
echo -e "${GREEN}修復腳本執行完成！${NC}"
