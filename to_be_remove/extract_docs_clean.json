{"parameters": {"jsCode": "// Google Docs API 文字提取\nconsole.log('=== GOOGLE DOCS API TEXT EXTRACTION ===');\nconsole.log('JSON response keys:', $json ? Object.keys($json) : 'No JSON');\n\nlet extractedText = '';\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\n\n// 從 Google Docs API 響應中提取文字\nif ($json && $json.body && $json.body.content) {\n  console.log('Processing Google Docs API response...');\n  fileName = $json.title || fileName;\n  fileId = $json.documentId || fileId;\n  \n  function extractTextFromContent(content) {\n    let text = '';\n    if (Array.isArray(content)) {\n      content.forEach(element => {\n        text += extractTextFromContent(element);\n      });\n    } else if (content && typeof content === 'object') {\n      if (content.textRun && content.textRun.content) {\n        text += content.textRun.content;\n      }\n      if (content.paragraph && content.paragraph.elements) {\n        text += extractTextFromContent(content.paragraph.elements);\n      }\n      if (content.table && content.table.tableRows) {\n        content.table.tableRows.forEach(row => {\n          if (row.tableCells) {\n            row.tableCells.forEach(cell => {\n              if (cell.content) {\n                text += extractTextFromContent(cell.content);\n              }\n            });\n          }\n        });\n      }\n    }\n    return text;\n  }\n  \n  try {\n    extractedText = extractTextFromContent($json.body.content);\n    console.log(`✅ Extracted ${extractedText.length} characters from Google Docs API`);\n  } catch (error) {\n    console.log('❌ Error extracting from Google Docs API:', error.message);\n  }\n}\n\n// 從上游節點獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  if (triggerData) {\n    fileName = triggerData.name || fileName;\n    fileId = triggerData.id || fileId;\n  }\n}\n\n// 最終結果\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ Google Docs API extraction failed');\n  extractedText = `No content extracted from ${fileName} via Google Docs API. File may be empty or API access insufficient.`;\n} else {\n  console.log(`✅ SUCCESS: Extracted ${extractedText.length} characters`);\n}\n\nreturn {\n  text: extractedText.trim(),\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs',\n  fullContent: extractedText.trim(),\n  extractionMethod: extractedText.includes('No content') ? 'error_fallback' : 'google_docs_api'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, 220], "id": "2257b3d3-0f1d-4950-897c-c733f17d234d", "name": "Extract Docs Text"}