{"name": "My workflow 4", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-2440, -60], "id": "8bf2b474-23c1-4eab-baa7-a8e753b66793", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Debug Google Drive Trigger output - 完整分析\nconst triggerData = $json;\n\nconsole.log('=== GOOGLE DRIVE TRIGGER DEBUG - 完整分析 ===');\nconsole.log('🔍 All trigger data keys:', Object.keys(triggerData));\nconsole.log('📄 Complete trigger data:', JSON.stringify(triggerData, null, 2));\n\n// 檢查所有可能包含檔名的欄位\nconst potentialNameFields = ['name', 'title', 'fileName', 'originalFilename', 'webViewLink', 'alternateLink'];\npotentialNameFields.forEach(field => {\n  if (triggerData[field]) {\n    console.log(`📂 Found ${field}:`, triggerData[field]);\n  }\n});\n\n// 檢查 exportLinks 是否包含檔名線索\nif (triggerData.exportLinks) {\n  console.log('📤 Export links available:', Object.keys(triggerData.exportLinks));\n  // 嘗試從 export URL 中提取檔名\n  Object.entries(triggerData.exportLinks).forEach(([format, url]) => {\n    if (url && typeof url === 'string') {\n      const match = url.match(/[?&]gid=([^&]*)|[?&]id=([^&]*)|[?&]export[^&]*name=([^&]*)/);\n      console.log(`📋 ${format} URL pattern:`, match ? match[0] : 'no match');\n    }\n  });\n}\n\nconsole.log('✅ Basic info:');\nconsole.log('  - ID:', triggerData.id);\nconsole.log('  - MimeType:', triggerData.mimeType);\nconsole.log('  - Has name field:', !!triggerData.name);\nconsole.log('=====================================');\n\n// Pass through all the data unchanged but add debugging flag\nreturn {\n  ...triggerData,\n  _debugTrigger: {\n    analysisComplete: true,\n    foundNameFields: potentialNameFields.filter(field => triggerData[field]),\n    timestamp: new Date().toISOString()\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2300, -60], "id": "debug-trigger", "name": "Debug Trigger Data"}, {"parameters": {"jsCode": "// 準備檔案元數據請求\nconst inputData = $json;\nconst fileId = inputData.id;\n\nif (!fileId) {\n  throw new Error('無法獲取檔案 ID 從 Debug Trigger Data');\n}\n\nconsole.log('🔍 Prepare File Metadata Request:');\nconsole.log('  - 檔案 ID:', fileId);\nconsole.log('  - 輸入數據 keys:', Object.keys(inputData));\n\n// 返回包含 fileId 的數據供下一個節點使用\nreturn {\n  ...inputData,\n  fileId: fileId,\n  _metadataRequest: {\n    prepared: true,\n    timestamp: new Date().toISOString()\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2220, -60], "id": "prepare-metadata-request", "name": "Prepare Metadata Request"}, {"parameters": {"url": "=https://www.googleapis.com/drive/v3/files/{{ $json.fileId }}?fields=id,name,mimeType,webViewLink", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-2080, -60], "id": "get-file-metadata", "name": "Get File Metadata", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理檔案元數據響應並合併\nconst metadataResponse = $json;\nconst originalTriggerData = $('Debug Trigger Data').first().json;\n\nconsole.log('🔄 Process File Metadata Response:');\nconsole.log('  - Metadata response keys:', Object.keys(metadataResponse));\nconsole.log('  - Metadata response:', JSON.stringify(metadataResponse, null, 2));\nconsole.log('  - Original trigger data keys:', Object.keys(originalTriggerData));\n\n// 檢查是否成功獲取檔名\nlet resolvedFileName = 'MetadataFailed';\nif (metadataResponse.name) {\n  resolvedFileName = metadataResponse.name;\n  console.log('✅ 成功從 API 獲取檔名:', resolvedFileName);\n} else {\n  console.log('❌ API 響應中沒有檔名');\n}\n\n// 合併所有數據\nconst mergedData = {\n  ...originalTriggerData,  // 保留原始觸發數據\n  ...metadataResponse,     // 覆蓋檔案元數據\n  resolvedFileName: resolvedFileName,\n  _metadataProcessing: {\n    processedAt: new Date().toISOString(),\n    success: resolvedFileName !== 'MetadataFailed',\n    source: 'Google Drive API'\n  }\n};\n\nconsole.log('🎯 合併數據完成:');\nconsole.log('  - 解析檔名:', resolvedFileName);\nconsole.log('  - 合併數據 keys:', Object.keys(mergedData));\n\nreturn mergedData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1940, -60], "id": "process-metadata-response", "name": "Process Metadata Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-2140, -60], "id": "00eb5bcc-9126-47ea-8189-9de7dec198f9", "name": "File Type Filter"}, {"parameters": {"jsCode": "// 檔名保護節點 - 強化版本，確保檔名傳遞到最終節點\n\nconst currentData = $json;\nconsole.log('📁 檔名保護節點 - 檢查數據:');\nconsole.log('  - 當前數據:', JSON.stringify(currentData, null, 2));\n\n// 嘗試從各個來源獲取檔名\nlet preservedFileName = 'ProtectionFailed';\nlet fileId = 'unknown-id';\n\n// 策略1: 從解析檔名獲取（最優先）\nif (currentData.resolvedFileName && currentData.resolvedFileName !== 'MetadataFailed') {\n  preservedFileName = currentData.resolvedFileName;\n  console.log('✅ 從解析檔名獲取:', preservedFileName);\n}\n// 策略2: 從當前數據的 name 欄位獲取\nelse if (currentData.name) {\n  preservedFileName = currentData.name;\n  console.log('✅ 從當前數據 name 欄位獲取檔名:', preservedFileName);\n}\n// 策略3: 從 Process Metadata Response 獲取\nelse if ($('Process Metadata Response').length > 0) {\n  const processedData = $('Process Metadata Response').first().json;\n  console.log('🔍 Process Metadata Response 數據:', JSON.stringify(processedData, null, 2));\n  if (processedData.resolvedFileName && processedData.resolvedFileName !== 'MetadataFailed') {\n    preservedFileName = processedData.resolvedFileName;\n    console.log('✅ 從 Process Metadata Response 獲取檔名:', preservedFileName);\n  } else if (processedData.name) {\n    preservedFileName = processedData.name;\n    console.log('✅ 從 Process Metadata Response name 獲取檔名:', preservedFileName);\n  }\n}\n\n// 策略4: 從 Debug Trigger Data 獲取\nif (preservedFileName === 'ProtectionFailed' && $('Debug Trigger Data').length > 0) {\n  const triggerData = $('Debug Trigger Data').first().json;\n  if (triggerData.id) {\n    fileId = triggerData.id;\n    console.log('📋 從 Debug Trigger Data 獲取 ID:', fileId);\n  }\n}\n\n// 獲取檔案ID\nif (currentData.id) {\n  fileId = currentData.id;\n}\n\n// 清理檔名\nif (preservedFileName && preservedFileName !== 'ProtectionFailed') {\n  preservedFileName = preservedFileName.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n}\n\n// 🎯 關鍵增強：創建全局檔名保護\nconst PROTECTED_FILENAME = preservedFileName;\nconst PROTECTED_FILE_ID = fileId;\n\nconsole.log('🛡️ 檔名保護結果:');\nconsole.log('  - 檔名:', preservedFileName);\nconsole.log('  - 檔案ID:', fileId);\nconsole.log('  - 全局保護檔名:', PROTECTED_FILENAME);\n\n// 返回完整的保護數據\nreturn {\n  ...currentData,  // 保留所有原始數據\n  name: preservedFileName,\n  id: fileId,\n  // 🎯 強化保護：多層次檔名保護\n  PROTECTED_FILENAME: PROTECTED_FILENAME,\n  PROTECTED_FILE_ID: PROTECTED_FILE_ID,\n  _filenameProtection: {\n    originalName: preservedFileName,\n    fileId: fileId,\n    protectedAt: 'After File Type Filter',\n    timestamp: new Date().toISOString(),\n    globalProtection: {\n      PROTECTED_FILENAME: PROTECTED_FILENAME,\n      PROTECTED_FILE_ID: PROTECTED_FILE_ID\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2000, -60], "id": "protect-filename", "name": "Protect Filename After Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1920, -120], "id": "c29ae29a-f61a-4d02-83f8-ff956ffd1e17", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 真實內容提取 - HTTP Export 方法 (Safe Version)\nconst item = $input.item;\n\n// 安全獲取檔案資訊\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\n\nif ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown Document';\n  fileId = filterData.id || 'unknown-id';\n} else {\n  console.log('Warning: File Type Filter node not available');\n}\n\nlet extractedText = '';\n\n// HTTP Request 節點可能會返回一個 JSON 物件，其中 data 是純文字\n// 或者，如果設定了 \"Response Format: String\"，它會直接返回純文字\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else {\n  throw new Error(`Could not find text content in the response from 'Export Google Docs as Text'. Response was: ${JSON.stringify(item.json)}`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  throw new Error(`Extracted text from Google Doc '${fileName}' is empty.`);\n}\n\n// 清理和格式化文本\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, ' ')\n  .replace(/\\n/g, ' ')\n  .replace(/\\s+/g, ' ')\n  .trim();\n\nreturn {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, -120], "id": "753e3484-ef31-4fd7-ba01-cf38c67d4a84", "name": "Extract Docs Text"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-1300, -60], "id": "75b1e2da-fb45-44e1-82b3-3868cf6f2630", "name": "Merge Extracted Content"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Do not add explanations or introductory text - only provide the translation\n\nText to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1100, -60], "id": "ad31c740-5071-43f2-a216-1fcc879adabd", "name": "First Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 處理 Gemini API 回應 (元數據保護版本)\nconst geminiResponse = $json;\n\n// 獲取源數據並保護元數據\nlet sourceData = { originalFileName: 'Unknown', fileType: 'unknown', originalData: null };\nif ($('Merge Extracted Content').length > 0) {\n  sourceData = $('Merge Extracted Content').first().json;\n}\n\n// 解析翻譯結果\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔄 Process Gemini Response - Preserving metadata:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - originalFileName:', sourceData.originalFileName);\nconsole.log('  - has _metadata:', !!sourceData._metadata);\n\n// 確保元數據完整傳遞\nconst preservedMetadata = sourceData._metadata || {\n  capturedAt: 'Process Gemini Response (fallback)',\n  originalFileName: sourceData.originalFileName,\n  timestamp: new Date().toISOString()\n};\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure,\n  // 保護並傳遞元數據\n  _metadata: {\n    ...preservedMetadata,\n    processedAt: 'Process Gemini Response',\n    translationLength: translatedText.length\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-900, -60], "id": "b2bea46e-c8b6-4014-a708-2c3e9ef1abef", "name": "Process Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-700, -60], "id": "7b35e2bf-c69a-459f-a043-728828e0c27f", "name": "Reflective Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 解析 Claude API 回應 (最終元數據保護版本)\nconst claudeResponse = $json;\n\n// 獲取上游數據並確保元數據完整性\nlet upstreamData = { initialTranslation: 'No translation', originalFileName: 'Unknown', fileType: 'unknown', originalData: null, originalStructure: null };\n\nif ($('Process Gemini Response').length > 0) {\n  upstreamData = $('Process Gemini Response').first().json;\n} else if ($('Merge Extracted Content').length > 0) {\n  const mergeData = $('Merge Extracted Content').first().json;\n  if (mergeData.fileType) {\n    upstreamData.fileType = mergeData.fileType;\n    upstreamData.originalFileName = mergeData.originalFileName || 'Unknown';\n    upstreamData.originalData = mergeData.originalData;\n    upstreamData.originalStructure = mergeData.originalStructure;\n  }\n}\n\n// 解析 Claude 回應的翻譯內容\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\n// 確保 fileType 不是 unknown\nif (upstreamData.fileType === 'unknown') {\n  if (upstreamData.originalFileName && upstreamData.originalFileName.includes('sheet')) {\n    upstreamData.fileType = 'sheets';\n  } else {\n    upstreamData.fileType = 'docs';\n  }\n}\n\n// 關鍵調試信息\nconsole.log('🎯 FINAL METADATA CHECK - Process Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - originalFileName:', upstreamData.originalFileName);\nconsole.log('  - has _metadata:', !!upstreamData._metadata);\nconsole.log('  - metadata source:', upstreamData._metadata?.capturedAt || 'none');\nconsole.log('=====================================');\n\n// 最終元數據保護\nconst finalMetadata = upstreamData._metadata || {\n  capturedAt: 'Process Claude Response (emergency fallback)',\n  originalFileName: upstreamData.originalFileName,\n  timestamp: new Date().toISOString()\n};\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure,\n  // 最終保護元數據\n  _metadata: {\n    ...finalMetadata,\n    finalProcessedAt: 'Process Claude Response',\n    finalTranslationLength: finalTranslation.length,\n    readyForOutput: true\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, -60], "id": "6538c9a6-9cc2-4f0f-b826-02a10dfbaa09", "name": "Process Claude Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileType }}", "rightValue": "never-match-docs", "operator": {"type": "string", "operation": "equals"}, "id": "docs-condition"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "sheets", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "sheets-condition-alt", "leftValue": "={{ $json.originalFileName }}", "rightValue": "sheet", "operator": {"type": "string", "operation": "contains", "name": "filter.operator.contains"}}], "combinator": "or"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fallback-sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "docs", "operator": {"type": "string", "operation": "notEquals", "name": "filter.operator.notEquals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": 2}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-300, -60], "id": "514cd145-9a9e-47a2-aa56-0985d0bf8af3", "name": "Output Type Switch"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容\nconst finalTranslation = $json.finalTranslation;\nconst originalFileName = $json.originalFileName.replace(/\\.(doc|docx|txt|pdf)$/i, '');\nconst finalTitle = `${originalFileName}_翻譯`;\n\nreturn {\n  title: finalTitle,\n  content: finalTranslation\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-240, -180], "id": "20cb8045-8e79-430e-a42b-983080cd4bbc", "name": "Prepare Docs Content"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-100, -120], "id": "ce1c42ff-d085-4f1c-94ab-f455207ccfb9", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [240, -120], "id": "13903ae8-5272-47fd-b278-0c79a8e5de97", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "aKNvhMNpGwKLTvmj", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "// Get the output from the \"Create Google Doc\" node (the current input)\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID from the \"Create Google Doc\" node.');\n}\n\n// Get the output from the \"Prepare Docs Content\" node (Safe Version)\nconst prepNodeItems = $('Prepare Docs Content').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Prepare Docs Content\" node.');\n}\n\n// Find the content from the first item of that node's output\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Prepare Docs Content\" node.');\n}\n\n// Return the combined payload\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [40, -120], "id": "e0819489-3eff-4a72-973b-ba103928431a", "name": "Prepare Final Payload"}, {"parameters": {"url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.id}/values/A:Z?valueRenderOption=UNFORMATTED_VALUE` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1920, 40], "id": "f2114c0c-644e-484e-940c-001068d5dadc", "name": "Read Sheet Data", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "/**\n * 處理 Google Sheets API 回應數據 - 檔名保護版本\n * 將結構化的 Sheets 數據轉換為翻譯用的文本格式\n * 重點：從檔名保護節點正確獲取檔名\n */\n\nconst apiResponse = $json;\nlet combinedText = '';\nlet originalDataForRebuild = [];\n\n// 檢查 API 回應格式\nif (apiResponse.values && Array.isArray(apiResponse.values)) {\n  const rows = apiResponse.values;\n  \n  rows.forEach((row, rowIndex) => {\n    originalDataForRebuild.push({\n      rowIndex: rowIndex,\n      values: row || []\n    });\n    \n    if (row && Array.isArray(row)) {\n      row.forEach(cell => {\n        const value = cell ? String(cell).trim() : '';\n        if (value) {\n          combinedText += value + '\\n';\n        }\n      });\n    }\n  });\n} else {\n  throw new Error('無效的 Google Sheets API 回應格式');\n}\n\n// 📁 核心修復：從檔名保護節點獲取檔名\nlet originalFileName = 'FormatSheetFailed';\nlet fileId = 'unknown-id';\nlet protectedMetadata = null;\n\nconsole.log('📁 Format Sheet Data - 開始檔名檢查');\n\n// 策略1：從檔名保護節點獲取（最優先，最可靠）\nif ($('Protect Filename After Filter').length > 0) {\n  const protectedData = $('Protect Filename After Filter').first().json;\n  console.log('🛡️ Protect Filename After Filter 數據:', JSON.stringify(protectedData, null, 2));\n  \n  // 🎯 優先使用全局保護檔名\n  if (protectedData.PROTECTED_FILENAME && protectedData.PROTECTED_FILENAME !== 'ProtectionFailed') {\n    originalFileName = protectedData.PROTECTED_FILENAME;\n    console.log('✅ 從全局保護檔名獲取:', originalFileName);\n  }\n  else if (protectedData.name && protectedData.name !== 'ProtectionFailed') {\n    originalFileName = protectedData.name;\n    console.log('✅ 從檔名保護節點獲取檔名:', originalFileName);\n  }\n  \n  if (protectedData.PROTECTED_FILE_ID) {\n    fileId = protectedData.PROTECTED_FILE_ID;\n  } else if (protectedData.id) {\n    fileId = protectedData.id;\n  }\n  \n  // 保存保護元數據以供後續傳遞\n  protectedMetadata = protectedData._filenameProtection;\n  \n  // 檢查保護資訊\n  if (protectedData._filenameProtection) {\n    console.log('🔍 檔名保護資訊:', JSON.stringify(protectedData._filenameProtection, null, 2));\n  }\n}\n\n// 策略2：從 Get File Metadata 獲取（備選）\nif (originalFileName === 'FormatSheetFailed' && $('Get File Metadata').length > 0) {\n  const metadataResponse = $('Get File Metadata').first().json;\n  console.log('📋 Get File Metadata 備選數據:', JSON.stringify(metadataResponse, null, 2));\n  \n  if (metadataResponse.name) {\n    originalFileName = metadataResponse.name;\n    console.log('✅ 從 Get File Metadata 備選獲取檔名:', originalFileName);\n  }\n}\n\n// 緊急修復：如果所有策略都失敗，嘗試最後的解決方案\nif (originalFileName === 'FormatSheetFailed') {\n  console.log('❌ 所有檔名獲取策略都失敗，開始緊急修復:');\n  \n  // 檢查 Debug Trigger Data 的 ID\n  if ($('Debug Trigger Data').length > 0) {\n    const debugData = $('Debug Trigger Data').first().json;\n    console.log('🔍 Debug Trigger Data 完整內容:', JSON.stringify(debugData, null, 2));\n    \n    if (debugData.id) {\n      // 使用文件ID的一部分作為檔名\n      originalFileName = 'Sheet_' + debugData.id.substring(0, 8);\n      console.log('🆘 使用文件ID生成檔名:', originalFileName);\n    }\n  }\n  \n  // 如果還是沒有，使用時間戳\n  if (originalFileName === 'FormatSheetFailed') {\n    originalFileName = 'Sheet_' + new Date().toISOString().substring(0, 10);\n    console.log('🆘 使用時間戳生成檔名:', originalFileName);\n  }\n}\n\n// 清理檔名\nif (originalFileName && originalFileName !== 'FormatSheetFailed') {\n  originalFileName = originalFileName.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n}\n\nconsole.log('🎯 FINAL - Format Sheet Data originalFileName:', originalFileName);\nconsole.log('📊 檔名獲取狀態:', originalFileName !== 'FormatSheetFailed' ? '✅ 成功' : '❌ 失敗');\n\nreturn {\n  text: combinedText.trim(),\n  originalFileId: fileId,\n  originalFileName: originalFileName,\n  fileType: 'sheets',\n  originalData: originalDataForRebuild,\n  originalStructure: apiResponse.values,\n  // 🎯 傳遞全局保護檔名\n  PROTECTED_FILENAME: originalFileName,\n  PROTECTED_FILE_ID: fileId,\n  // 添加強化元數據保護\n  _metadata: {\n    capturedAt: 'Format Sheet Data',\n    originalFileName: originalFileName,\n    fileId: fileId,\n    timestamp: new Date().toISOString(),\n    protectionSource: originalFileName !== 'FormatSheetFailed' ? 'success' : 'failed',\n    protectedMetadata: protectedMetadata,\n    globalProtection: {\n      PROTECTED_FILENAME: originalFileName,\n      PROTECTED_FILE_ID: fileId\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, 40], "id": "b73bf8c7-70b8-4656-bfce-3c9c42abb931", "name": "Format Sheet Data"}, {"parameters": {"jsCode": "// Rebuild Translated Sheet Data (Fallback Version)\n\nconst translatedText = $json.finalTranslation;\nlet originalStructure = $json.originalStructure;\n\n// 如果 originalStructure 不存在，嘗試從其他節點獲取\nif (!originalStructure) {\n  console.log('originalStructure not found in current input, trying other nodes...');\n  \n  // 嘗試從 Format Sheet Data 獲取\n  if ($('Format Sheet Data').length > 0) {\n    const formatData = $('Format Sheet Data').first().json;\n    originalStructure = formatData.originalStructure;\n    console.log('Tried Format Sheet Data:', !!originalStructure);\n  }\n  \n  // 嘗試從 Merge Extracted Content 獲取\n  if (!originalStructure && $('Merge Extracted Content').length > 0) {\n    const mergeData = $('Merge Extracted Content').first().json;\n    originalStructure = mergeData.originalStructure;\n    console.log('Tried Merge Extracted Content:', !!originalStructure);\n  }\n}\n\n// 如果還是沒有 originalStructure，創建一個默認結構\nif (!originalStructure) {\n  console.log('No originalStructure found, creating simple default structure...');\n  // 將翻譯文本按行分割，每行作為一個儲存格\n  const lines = translatedText.split('\\n').filter(line => line.trim() !== '');\n  originalStructure = lines.map(line => [line]); // 每行一個儲存格\n  console.log('Created default structure with', originalStructure.length, 'rows');\n}\n\n// 將翻譯文本分割為行\nconst translatedLines = translatedText.split('\\n').filter(line => line.trim() !== '');\nlet lineIndex = 0;\n\n// 重建為 Google Sheets API 格式: [[cell1, cell2], [cell3, cell4]]\nconst rebuiltValues = [];\n\nfor (let rowIndex = 0; rowIndex < originalStructure.length; rowIndex++) {\n  const originalRow = originalStructure[rowIndex] || [];\n  const newRow = [];\n  \n  for (let colIndex = 0; colIndex < originalRow.length; colIndex++) {\n    if (lineIndex < translatedLines.length) {\n      // 使用翻譯內容\n      newRow.push(translatedLines[lineIndex].trim());\n      lineIndex++;\n    } else {\n      // 如果翻譯內容不足，使用原始內容或空字符串\n      newRow.push(originalRow[colIndex] || '');\n    }\n  }\n  \n  rebuiltValues.push(newRow);\n}\n\nreturn {\n  values: rebuiltValues,\n  range: 'A:Z',\n  majorDimension: 'ROWS'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-180, 140], "id": "c8ecf58d-da7d-413d-96eb-67a1dd95ae89", "name": "Rebuild Translated Sheet Data"}, {"parameters": {"jsCode": "// 🎯 最終檔名決策 - 使用元數據保護系統\n\nconst inputData = $json;\nconsole.log('🔍 Prepare Filename - 元數據檢查開始');\nconsole.log('Input data keys:', Object.keys(inputData));\n\nlet originalFileName = 'EmergencyFallback';\nlet metadataSource = 'none';\n\n// 策略1: 從保護的元數據獲取（最優先）\nif (inputData._metadata && inputData._metadata.originalFileName) {\n  originalFileName = inputData._metadata.originalFileName;\n  metadataSource = inputData._metadata.capturedAt || 'metadata';\n  console.log('✅ 從保護元數據獲取檔名:', originalFileName, '來源:', metadataSource);\n}\n// 策略2: 從直接數據獲取\nelse if (inputData.originalFileName && inputData.originalFileName !== 'Unknown' && inputData.originalFileName !== 'Unknown Sheet' && inputData.originalFileName !== 'DefaultSheet') {\n  originalFileName = inputData.originalFileName;\n  metadataSource = 'direct';\n  console.log('📋 從直接數據獲取檔名:', originalFileName);\n}\n// 策略3: 回溯到 Process Claude Response\nelse if ($('Process Claude Response').length > 0) {\n  const claudeData = $('Process Claude Response').first().json;\n  if (claudeData._metadata && claudeData._metadata.originalFileName) {\n    originalFileName = claudeData._metadata.originalFileName;\n    metadataSource = 'claude-metadata';\n    console.log('🔄 從 Claude Response 元數據獲取檔名:', originalFileName);\n  } else if (claudeData.originalFileName && claudeData.originalFileName !== 'Unknown') {\n    originalFileName = claudeData.originalFileName;\n    metadataSource = 'claude-direct';\n    console.log('📄 從 Claude Response 直接數據獲取檔名:', originalFileName);\n  }\n}\n\n// 最終清理\nif (originalFileName && originalFileName !== 'EmergencyFallback') {\n  originalFileName = originalFileName.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n}\n\nconst finalTitle = originalFileName + '_翻譯版';\n\n// 最終報告\nconsole.log('🎯 最終決策結果:');\nconsole.log('  - 原始檔名:', originalFileName);\nconsole.log('  - 資料來源:', metadataSource);\nconsole.log('  - 最終標題:', finalTitle);\nconsole.log('  - 成功率:', originalFileName !== 'EmergencyFallback' ? '✅ 成功' : '❌ 失敗');\n\nreturn {\n  rebuiltData: inputData,\n  originalFileName: originalFileName,\n  sheetTitle: finalTitle,\n  _metadata: {\n    ...inputData._metadata,\n    finalCaptureSource: metadataSource,\n    finalDecisionAt: 'Prepare Filename for Sheet Creation'\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, 140], "id": "prepare-filename", "name": "Prepare Filename for Sheet Creation"}, {"parameters": {"url": "=https://www.googleapis.com/drive/v3/files/{{ $('Debug Trigger Data').first().json.id }}?fields=id,name,mimeType", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [20, 140], "id": "get-real-filename", "name": "Get Real Filename Before Create", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 最終檔名決策 - 簡化版本\n\nconst prepareData = $('Prepare Filename for Sheet Creation').first().json;\n\nconsole.log('🎯 最終檔名決策 - 簡化版本:');\nconsole.log('  - 準備數據:', JSON.stringify(prepareData, null, 2));\n\n// 檢查所有可能的檔名來源\nconst debugTriggerData = $('Debug Trigger Data').length > 0 ? $('Debug Trigger Data').first().json : null;\nconst getFileMetadata = $('Get File Metadata').length > 0 ? $('Get File Metadata').first().json : null;\n\nconsole.log('  - Debug Trigger有', Object.keys(debugTriggerData || {}).length, '個欄位');\nconsole.log('  - Get File Metadata有', Object.keys(getFileMetadata || {}).length, '個欄位');\n\nlet finalFileName = 'WorkingOnIt';\n\n// 策略1: 從檔名保護節點獲取（最優先）\nconst protectFilenameData = $('Protect Filename After Filter').length > 0 ? $('Protect Filename After Filter').first().json : null;\nif (protectFilenameData && protectFilenameData.name && protectFilenameData.name !== 'ProtectionFailed') {\n  finalFileName = protectFilenameData.name.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n  console.log('🛡️ 從檔名保護節點獲取檔名:', finalFileName);\n  console.log('🔍 保護節點完整數據:', JSON.stringify(protectFilenameData._filenameProtection, null, 2));\n}\n// 策略2: 從 Get File Metadata 獲取\nelse if (getFileMetadata && getFileMetadata.name) {\n  finalFileName = getFileMetadata.name.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n  console.log('✅ 從 Get File Metadata 獲取檔名:', finalFileName);\n}\n// 策略2: 如果有觸發數據，顯示它\nelse if (debugTriggerData) {\n  console.log('🔍 Debug Trigger Data 內容:', JSON.stringify(debugTriggerData, null, 2));\n  \n  // 嘗試從匯出鏈接中提取檔名\n  if (debugTriggerData.exportLinks) {\n    const exportLinks = debugTriggerData.exportLinks;\n    console.log('📄 找到匯出鏈接，嘗試解析檔名...');\n    \n    // 從匯出鏈接中查找可能的檔名線索\n    for (const [format, link] of Object.entries(exportLinks)) {\n      if (typeof link === 'string' && link.includes('id=')) {\n        console.log(`  - ${format}: ${link}`);\n      }\n    }\n  }\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\nconsole.log('🏆 最終決策結果 - 檔名:', finalTitle);\n\nreturn {\n  rebuiltData: prepareData.rebuiltData,\n  originalFileName: finalFileName,\n  sheetTitle: finalTitle,\n  debugInfo: {\n    hasTriggerData: !!debugTriggerData,\n    hasMetadata: !!getFileMetadata,\n    metadataKeys: getFileMetadata ? Object.keys(getFileMetadata) : [],\n    triggerKeys: debugTriggerData ? Object.keys(debugTriggerData) : []\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [120, 140], "id": "merge-final-filename", "name": "Merge Final Filename"}, {"parameters": {"jsCode": "// 🎯 實時解決方案：重新調用 Google Drive API 獲取檔名\n\nconst inputData = $json;\nconst fileId = $('Debug Trigger Data').first().json.id; // 獲取檔案ID\n\nconsole.log('🔍 Final Filename Resolver - 重新調用 API 獲取檔名');\nconsole.log('  - 檔案ID:', fileId);\n\n// 準備 API 調用來獲取檔名\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行API調用');\n}\n\n// 使用檔案ID作為臨時檔名，實際檔名將在下一個節點獲取\nconst tempFileName = 'FileID_' + fileId.substring(0, 8);\n\nconsole.log('🏗️ 準備API調用:');\nconsole.log('  - 臨時檔名:', tempFileName);\nconsole.log('  - 將在下一個節點調用API獲取真實檔名');\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  tempFileName: tempFileName,\n  fileIdForAPI: fileId,\n  needsAPICall: true,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`,\n  sourceInfo: {\n    resolvedAt: 'Final Filename Resolver (API Prep)',\n    timestamp: new Date().toISOString(),\n    strategy: 'prepare-api-call'\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [220, 140], "id": "final-filename-resolver", "name": "Final Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [320, 140], "id": "real-time-filename-api", "name": "Real-time Filename API Call", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理實時API調用結果並設定最終檔名\n\nconst apiResponse = $json;\nconst previousData = $('Final Filename Resolver').first().json;\n\nconsole.log('🎯 Real-time Filename Processing:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\nconsole.log('  - Previous Data keys:', Object.keys(previousData));\n\nlet finalFileName = 'APICallFailed';\n\n// 從API響應獲取檔名\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n  console.log('✅ 從實時API獲取檔名:', finalFileName);\n} else {\n  console.log('❌ API響應沒有檔名，使用備案');\n  finalFileName = 'RealTimeAPI_' + new Date().toISOString().substring(0, 16).replace(/[:T]/g, '_');\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終檔名解析結果:');\nconsole.log('  - 檔名:', finalFileName);\nconsole.log('  - 標題:', finalTitle);\n\n// 合併所有必要的數據\nreturn {\n  ...previousData.rebuiltData || previousData,  // 保留重建的sheet數據\n  finalSheetTitle: finalTitle,\n  finalFileName: finalFileName,\n  realTimeResolution: {\n    success: finalFileName !== 'APICallFailed',\n    resolvedAt: new Date().toISOString(),\n    source: 'Real-time Google Drive API'\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, 140], "id": "process-realtime-filename", "name": "Process Real-time Filename"}, {"parameters": {"method": "PUT", "url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.spreadsheetId}/values/${encodeURIComponent($json.range)}?valueInputOption=RAW` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  values: $json.values,\n  majorDimension: 'ROWS'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [240, 60], "id": "0cc77a17-b3fc-4e37-b724-2f55962a5a16", "name": "Write Translation to New Google Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "https://sheets.googleapis.com/v4/spreadsheets", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  properties: {\n    title: $json.finalSheetTitle || 'FinalFallback_翻譯版'\n  },\n  sheets: [{\n    properties: {\n      title: '翻譯結果',\n      gridProperties: {\n        rowCount: 1000,\n        columnCount: 26\n      }\n    }\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 20], "id": "********-c614-487c-815e-e0cf02aa60d2", "name": "Create New Translated Google Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Prepare Sheet Payload (New Google Sheets File)\n\n// 1. Get the new spreadsheet ID from the create response (current input)\nconst createResponse = $json;\nconst newSpreadsheetId = createResponse.spreadsheetId;\n\nif (!newSpreadsheetId) {\n  console.log('Create response:', JSON.stringify(createResponse, null, 2));\n  throw new Error(\"Could not get new spreadsheet ID from create response. Response: \" + JSON.stringify(createResponse));\n}\n\n// 2. Get the rebuilt data from the parallel step\nconst rebuiltDataItems = $('Rebuild Translated Sheet Data').all();\nif (!rebuiltDataItems || rebuiltDataItems.length === 0) {\n  throw new Error(\"Could not retrieve data from 'Rebuild Translated Sheet Data' node.\");\n}\n\n// 3. Extract the values array from the rebuilt data\nconst rebuiltData = rebuiltDataItems[0].json;\nconst valuesToWrite = rebuiltData.values || [];\n\nconsole.log('New spreadsheet ID:', newSpreadsheetId);\nconsole.log('Values to write count:', valuesToWrite.length);\n\n// 4. Return the payload for writing to the new Google Sheets file\nreturn {\n  spreadsheetId: newSpreadsheetId,\n  values: valuesToWrite,\n  range: '翻譯結果!A:Z',\n  majorDimension: 'ROWS',\n  newFileUrl: `https://docs.google.com/spreadsheets/d/${newSpreadsheetId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [40, 40], "id": "3ab04be2-8ada-446a-994a-36e3dd6dd681", "name": "Prepare Sheet Payload"}, {"parameters": {"method": "PATCH", "url": "={{ `https://www.googleapis.com/drive/v3/files/${$json.spreadsheetId}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "addParents", "value": "1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg"}, {"name": "fields", "value": "id,parents"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [140, 40], "id": "move-sheet-to-folder", "name": "Move Sheet to Output Folder", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Reconstruct payload after moving file\n\n// Get the original payload data from Prepare Sheet Payload\nconst prepareSheetItems = $('Prepare Sheet Payload').all();\nif (!prepareSheetItems || prepareSheetItems.length === 0) {\n  throw new Error('Could not retrieve data from Prepare Sheet Payload node.');\n}\n\nconst originalPayload = prepareSheetItems[0].json;\n\n// Return the complete payload needed for writing to Google Sheets\nreturn {\n  spreadsheetId: originalPayload.spreadsheetId,\n  values: originalPayload.values,\n  range: originalPayload.range,\n  majorDimension: originalPayload.majorDimension,\n  newFileUrl: originalPayload.newFileUrl\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [240, 40], "id": "reconstruct-payload", "name": "Reconstruct Write Payload"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Debug Trigger Data", "type": "main", "index": 0}]]}, "Debug Trigger Data": {"main": [[{"node": "Prepare Metadata Request", "type": "main", "index": 0}]]}, "Prepare Metadata Request": {"main": [[{"node": "Get File Metadata", "type": "main", "index": 0}]]}, "Get File Metadata": {"main": [[{"node": "Process Metadata Response", "type": "main", "index": 0}]]}, "Process Metadata Response": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Protect Filename After Filter", "type": "main", "index": 0}]]}, "Protect Filename After Filter": {"main": [[{"node": "Read Sheet Data", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Merge Extracted Content": {"main": [[{"node": "First Translation (<PERSON>)", "type": "main", "index": 0}]]}, "First Translation (Gemini)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>)", "type": "main", "index": 0}]]}, "Reflective Translation (Claude)": {"main": [[{"node": "Process Claude Response", "type": "main", "index": 0}]]}, "Process Claude Response": {"main": [[{"node": "Output Type Switch", "type": "main", "index": 0}]]}, "Output Type Switch": {"main": [[{"node": "Prepare Docs Content", "type": "main", "index": 0}], [{"node": "Rebuild Translated Sheet Data", "type": "main", "index": 0}], [{"node": "Rebuild Translated Sheet Data", "type": "main", "index": 0}]]}, "Prepare Docs Content": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}, "Read Sheet Data": {"main": [[{"node": "Format Sheet Data", "type": "main", "index": 0}]]}, "Format Sheet Data": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 1}]]}, "Rebuild Translated Sheet Data": {"main": [[{"node": "Prepare Filename for Sheet Creation", "type": "main", "index": 0}]]}, "Prepare Filename for Sheet Creation": {"main": [[{"node": "Merge Final Filename", "type": "main", "index": 0}]]}, "Merge Final Filename": {"main": [[{"node": "Final Filename Resolver", "type": "main", "index": 0}]]}, "Final Filename Resolver": {"main": [[{"node": "Real-time Filename API Call", "type": "main", "index": 0}]]}, "Real-time Filename API Call": {"main": [[{"node": "Process Real-time Filename", "type": "main", "index": 0}]]}, "Process Real-time Filename": {"main": [[{"node": "Create New Translated Google Sheet", "type": "main", "index": 0}]]}, "Create New Translated Google Sheet": {"main": [[{"node": "Prepare Sheet Payload", "type": "main", "index": 0}]]}, "Prepare Sheet Payload": {"main": [[{"node": "Move Sheet to Output Folder", "type": "main", "index": 0}]]}, "Move Sheet to Output Folder": {"main": [[{"node": "Reconstruct Write Payload", "type": "main", "index": 0}]]}, "Reconstruct Write Payload": {"main": [[{"node": "Write Translation to New Google Sheet", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "e5e4a4d8-5040-4a4f-8fca-5ca8786b18a4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}