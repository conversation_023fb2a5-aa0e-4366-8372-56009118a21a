// Word 檔案內容提取 - 通過 Google Docs 轉換方式
const item = $input.item;

console.log('📄 Extract Word Text via Google Docs Conversion:');
console.log('  - Input item keys:', Object.keys(item));

let fileName = 'Unknown Word Document';
let fileId = 'unknown-id';

// 從觸發器獲取原始文件信息
if ($('Google Drive Trigger').length > 0) {
  const triggerData = $('Google Drive Trigger').first().json;
  fileName = triggerData.name || 'Unknown Word Document';
  fileId = triggerData.id || 'unknown-id';
  console.log('  - 從觸發器獲取原始文件名:', fileName);
} else if ($('File Type Filter').length > 0) {
  const filterData = $('File Type Filter').first().json;
  fileName = filterData.name || 'Unknown Word Document';
  fileId = filterData.id || 'unknown-id';
  console.log('  - 從過濾器獲取原始文件名:', fileName);
}

let extractedText = '';

// 從轉換後的 Google Docs 提取文本
if (typeof item.json === 'string') {
  extractedText = item.json;
} else if (item.json && typeof item.json.data === 'string') {
  extractedText = item.json.data;
} else {
  console.log('❌ 無法找到文本內容');
  throw new Error(`Could not find text content in the converted document response`);
}

if (!extractedText || extractedText.trim().length === 0) {
  console.log('❌ 提取的文本為空');
  throw new Error(`Extracted text from converted Word document is empty.`);
}

const cleanedText = extractedText
  .replace(/\r\n/g, '\n')
  .replace(/\r/g, '\n')
  .trim();

const result = {
  text: cleanedText,
  originalFileId: fileId,
  originalFileName: fileName,
  fileType: 'word' // 保持為 word 類型
};

console.log('✅ Word 文本提取完成 (via Google Docs):');
console.log('  - fileType:', result.fileType);
console.log('  - fileName:', result.originalFileName);
console.log('  - textLength:', result.text.length);

// 清理臨時轉換的文件
try {
  const convertedDocId = $('Convert Word to Google Docs').first().json.id;
  if (convertedDocId) {
    console.log('🗑️ 準備清理臨時文件:', convertedDocId);
    // 注意：實際的清理將在後續節點中進行
  }
} catch (cleanupError) {
  console.log('⚠️ 無法獲取臨時文件ID進行清理:', cleanupError.message);
}

return result;
