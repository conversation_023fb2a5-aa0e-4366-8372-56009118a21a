{"name": "My workflow 4", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, -20], "id": "55022b48-cb67-43ce-b31d-0acd593de215", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-920, -20], "id": "1d34b768-fc56-4092-8628-386bf9fde97e", "name": "File Type Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-700, -80], "id": "cdffe0bc-95c9-44d1-af30-fd8640d2503f", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"sheetsToFormat": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-700, 60], "id": "910a2a2b-51fe-4e6f-9398-9145fbc7630e", "name": "Download Google Sheets as Excel", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 真實內容提取 - HTTP Export 方法\nconsole.log('=== GOOGLE DOCS REAL CONTENT EXTRACTION ===');\n\nlet extractedText = '';\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\nlet debugInfo = {};\n\nconsole.log('HTTP Response type:', typeof $json);\nconsole.log('HTTP Response data:', $json);\n\n// 從 HTTP Export 響應中提取純文本內容\nif (typeof $json === 'string' && $json.trim().length > 0) {\n  // 直接文本響應\n  extractedText = $json.trim();\n  debugInfo.method = 'http_export_string';\n  console.log('✅ Got string response from Google Docs export');\n  \n} else if ($json && typeof $json === 'object') {\n  // 檢查可能的文本字段\n  const textFields = ['text', 'content', 'body', 'data'];\n  \n  for (const field of textFields) {\n    if ($json[field] && typeof $json[field] === 'string' && $json[field].trim().length > 0) {\n      extractedText = $json[field].trim();\n      debugInfo.method = `http_export_object_${field}`;\n      console.log(`✅ Found text content in field: ${field}`);\n      break;\n    }\n  }\n  \n  // 如果是對象但沒有找到文本，記錄結構\n  if (!extractedText) {\n    debugInfo.responseKeys = Object.keys($json);\n    console.log('❌ Object response but no text content found');\n    console.log('Available keys:', Object.keys($json));\n  }\n  \n} else {\n  console.log('❌ Unexpected response type or empty response');\n  debugInfo.method = 'unexpected_response';\n  debugInfo.responseType = typeof $json;\n}\n\n// 從上游節點獲取檔案信息\nconsole.log('🔍 Extracting filename from upstream nodes...');\n\nif ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  console.log('File Type Filter data:', JSON.stringify(filterData, null, 2));\n  if (filterData && filterData.name) {\n    fileName = filterData.name;\n    fileId = filterData.id || fileId;\n    console.log('✅ Got filename from File Type Filter:', fileName);\n  }\n} else {\n  console.log('❌ File Type Filter node not available');\n}\n\nif (!fileName || fileName === 'Unknown Document') {\n  if ($('Google Drive Trigger').length > 0) {\n    const triggerData = $('Google Drive Trigger').first().json;\n    console.log('Google Drive Trigger data:', JSON.stringify(triggerData, null, 2));\n    if (triggerData && triggerData.name) {\n      fileName = triggerData.name;\n      fileId = triggerData.id || fileId;\n      console.log('✅ Got filename from Google Drive Trigger:', fileName);\n    }\n  } else {\n    console.log('❌ Google Drive Trigger node not available');\n  }\n}\n\nconsole.log('📝 Final filename determined:', fileName);\nconsole.log('🆔 Final file ID determined:', fileId);\n\n// 處理提取結果\nif (extractedText && extractedText.length > 0) {\n  // 清理和格式化文本\n  extractedText = extractedText\n    .replace(/\\r\\n/g, ' ')     // 替換 Windows 換行\n    .replace(/\\n/g, ' ')       // 替換 Unix 換行\n    .replace(/\\s+/g, ' ')      // 合併多個空格\n    .trim();\n  \n  // 確保內容不為空\n  if (extractedText.length === 0) {\n    console.log('❌ Content became empty after cleaning');\n    debugInfo.cleaningIssue = true;\n  } else {\n    console.log(`📄 Processing file: ${fileName} (${fileId})`);\n    console.log(`📝 Final extracted text: ${extractedText.length} characters`);\n    console.log('📖 Content preview:', extractedText.substring(0, 300) + '...');\n    \n    return {\n      text: extractedText,\n      originalFileId: fileId,\n      originalFileName: fileName,\n      fileType: 'docs',\n      extractionMethod: debugInfo.method,\n      contentLength: extractedText.length,\n      debugInfo: debugInfo\n    };\n  }\n}\n\n// 如果提取失敗，返回錯誤信息\nconsole.log('❌ Failed to extract any text content from Google Docs export');\nconsole.log('Debug info:', JSON.stringify(debugInfo, null, 2));\n\nreturn {\n  text: `ERROR: Failed to extract content from Google Docs '${fileName}' (${fileId}). Export may have failed or document is empty.`,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs',\n  extractionMethod: 'failed',\n  contentLength: 0,\n  debugInfo: debugInfo\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-480, -80], "id": "43e1aa96-559e-474d-95f0-968088e0bb6e", "name": "Extract Docs Text"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [-480, 60], "id": "a3be875e-613f-40f9-8c90-56bdbb89434c", "name": "Parse Sheets Content"}, {"parameters": {"jsCode": "// 處理 Google Sheets 內容，將所有文字內容合併\nconst sheetsData = $input.all();\nlet combinedText = '';\n\nconsole.log('=== EXTRACT SHEETS TEXT ===');\nconsole.log('Sheets data length:', sheetsData.length);\n\n// 遍歷所有工作表數據\nsheetsData.forEach((sheet, sheetIndex) => {\n  if (sheet.json && typeof sheet.json === 'object') {\n    // 遍歷每個欄位\n    Object.keys(sheet.json).forEach(key => {\n      const value = sheet.json[key];\n      if (typeof value === 'string' && value.trim()) {\n        combinedText += value + '\\n';\n      }\n    });\n  }\n});\n\n// 嘗試從 File Type Filter 或 Google Drive Trigger 獲取文件信息\nlet fileData = null;\nif ($('File Type Filter').length > 0) {\n  fileData = $('File Type Filter').first().json;\n  console.log('✅ Got file data from File Type Filter:', fileData?.name);\n} else if ($('Google Drive Trigger').length > 0) {\n  fileData = $('Google Drive Trigger').first().json;\n  console.log('✅ Got file data from Google Drive Trigger:', fileData?.name);\n} else {\n  console.log('❌ No file data available from upstream nodes');\n  fileData = {};\n}\n\nconst result = {\n  text: combinedText.trim(),\n  originalFileId: fileData?.id || '',\n  originalFileName: fileData?.name || 'Unknown Sheets',\n  fileType: 'sheets',\n  originalData: sheetsData\n};\n\nconsole.log('📊 Sheets extraction result:', {\n  textLength: result.text.length,\n  fileName: result.originalFileName,\n  fileId: result.originalFileId\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-280, 60], "id": "93660aa7-ebe2-45a6-a68d-fa09c5756531", "name": "Extract Sheets Text"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-80, -20], "id": "d798bfcf-d25a-4890-be00-11f4c01bf7e4", "name": "Merge Extracted Content"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a precise translation engine. The following input consists of text segments separated by newlines. Your task is to translate each segment into Traditional Chinese (Taiwan).\n**Crucially, you MUST preserve the exact number of newlines.** Do not add any introductory text, explanations, or merge lines. The number of lines in your output must exactly match the number of lines in the input.\n\nInput Text:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [120, -20], "id": "def5b6e7-7fbe-4410-9dfe-4acb5cf83560", "name": "First Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 處理 Gemini API 回應\nconst geminiResponse = $json;\n\n// 嘗試從多個源獲取數據\nlet sourceData = {};\n\n// 首先嘗試從 Merge Extracted Content 獲取\nif ($('Merge Extracted Content').length > 0) {\n  sourceData = $('Merge Extracted Content').first().json;\n  console.log('✅ Got source data from Merge Extracted Content');\n} \n// 如果 Merge 沒有數據，嘗試直接從 Extract Docs Text 獲取\nelse if ($('Extract Docs Text').length > 0) {\n  sourceData = $('Extract Docs Text').first().json;\n  console.log('✅ Got source data from Extract Docs Text (fallback)');\n}\n// 最後嘗試從 Extract Sheets Text 獲取\nelse if ($('Extract Sheets Text').length > 0) {\n  sourceData = $('Extract Sheets Text').first().json;\n  console.log('✅ Got source data from Extract Sheets Text (fallback)');\n}\nelse {\n  console.log('❌ No source data available from any extraction node');\n}\n\nconsole.log('=== GEMINI API RESPONSE ===');\nconsole.log('Gemini response:', JSON.stringify(geminiResponse, null, 2));\nconsole.log('Source data:', JSON.stringify(sourceData, null, 2));\n\n// 解析翻譯結果\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('Extracted translation:', translatedText);\n\n// 檢查並記錄每個字段\nconsole.log('🔍 Analyzing source data fields:');\nconsole.log('- originalText:', sourceData.text ? `Present (${sourceData.text.length} chars)` : 'Missing');\nconsole.log('- originalFileName:', sourceData.originalFileName || 'Missing');\nconsole.log('- originalFileId:', sourceData.originalFileId || 'Missing');\nconsole.log('- fileType:', sourceData.fileType || 'Missing');\nconsole.log('- fileType actual value:', JSON.stringify(sourceData.fileType));\nconsole.log('- originalData:', sourceData.originalData ? 'Present' : 'Missing');\nconsole.log('- Full sourceData keys:', Object.keys(sourceData));\n\n// 傳遞完整數據到 Claude 進行改進\nconst result = {\n  initialTranslation: translatedText,\n  originalText: sourceData.text || 'No original text',\n  originalFileName: sourceData.originalFileName || 'Unknown',\n  originalFileId: sourceData.originalFileId || '',\n  fileType: sourceData.fileType || 'unknown',\n  originalData: sourceData.originalData\n};\n\nconsole.log('📤 Data being passed to Claude:');\nconsole.log('- fileType:', result.fileType);\nconsole.log('- originalFileName:', result.originalFileName);\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, -20], "id": "fddc3967-3398-44d9-a8cd-80d69bbb2f03", "name": "Process Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, -20], "id": "870fa7ed-a32d-44f8-943b-89914306ce09", "name": "Reflective Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 解析 Claude API 回應\nconst claudeResponse = $json;\nconst upstreamData = $('Process Gemini Response').length > 0 ? \n  $('Process Gemini Response').first().json : {};\n\nconsole.log('=== PROCESS CLAUDE RESPONSE ===');\nconsole.log('Claude response:', JSON.stringify(claudeResponse, null, 2));\nconsole.log('Upstream data from Gemini:', JSON.stringify(upstreamData, null, 2));\n\nlet finalTranslation = upstreamData.initialTranslation || 'No translation available';\n\ntry {\n  if (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n    finalTranslation = claudeResponse.content[0].text;\n    console.log('✅ Using Claude improved translation');\n  } else {\n    console.log('⚠️ Using Gemini initial translation as fallback');\n  }\n} catch (e) {\n  console.error('解析 Claude 回應時出錯:', e.message);\n}\n\n// 確定文件類型：如果我們到達這裡，說明是通過 Gemini 翻譯流程的，\n// 而 Gemini 只處理合併後的文本，我們需要推斷文件類型\nlet determinedFileType = upstreamData.fileType || 'unknown';\n\n// 如果 fileType 是 unknown，嘗試從其他信息推斷\nif (determinedFileType === 'unknown') {\n  // 從 originalData 的結構推斷（更安全的方法）\n  if (upstreamData.originalData && Array.isArray(upstreamData.originalData)) {\n    determinedFileType = 'sheets';\n    console.log('🔧 Inferred fileType as \"sheets\" from originalData array structure');\n  }\n  else {\n    // 簡單默認為 docs，避免節點引用問題\n    determinedFileType = 'docs';\n    console.log('🔧 Defaulting fileType to \"docs\" as fallback');\n  }\n}\n\n// 傳遞最終數據到輸出交換機\nconst result = {\n  finalTranslation: finalTranslation.trim(),\n  originalFileId: upstreamData.originalFileId || '',\n  originalFileName: upstreamData.originalFileName || 'Unknown',\n  fileType: determinedFileType,\n  originalData: upstreamData.originalData\n};\n\nconsole.log('🔄 Data being passed to Output Type Switch:');\nconsole.log('- finalTranslation length:', result.finalTranslation.length);\nconsole.log('- originalFileName:', result.originalFileName);\nconsole.log('- fileType:', result.fileType);\nconsole.log('- fileType actual value:', JSON.stringify(result.fileType));\nconsole.log('- originalFileId:', result.originalFileId);\nconsole.log('🔍 Upstream data fileType:', JSON.stringify(upstreamData.fileType));\nconsole.log('🔍 All upstream data keys:', Object.keys(upstreamData));\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, -20], "id": "6da134e6-c854-4d70-9592-d94486060753", "name": "Process Claude Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileType }}", "rightValue": "docs", "operator": {"type": "string", "operation": "equals"}, "id": "docs-condition"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "sheets", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [920, -20], "id": "f5f91369-8307-448a-b5ea-37084105a4cf", "name": "Output Type Switch"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容 - 傳遞翻譯內容\nconsole.log('=== PREPARE DOCS CONTENT ===');\nconsole.log('Input from Output Type Switch:', JSON.stringify($json, null, 2));\n\n// 獲取翻譯內容\nlet finalTranslation = $json.finalTranslation || '';\nlet originalFileName = $json.originalFileName || '';\n\nconsole.log('📋 Input data check:');\nconsole.log('- finalTranslation:', finalTranslation ? `${finalTranslation.length} chars` : 'Missing');\nconsole.log('- originalFileName:', originalFileName || 'Missing');\n\n// 檢查翻譯內容\nif (!finalTranslation || finalTranslation.trim().length === 0) {\n  console.log('❌ No finalTranslation found, checking other fields...');\n  const possibleFields = ['finalTranslation', 'initialTranslation', 'content', 'text'];\n  for (const field of possibleFields) {\n    if ($json[field] && typeof $json[field] === 'string' && $json[field].trim().length > 0) {\n      finalTranslation = $json[field];\n      console.log(`✅ Found content in ${field}`);\n      break;\n    }\n  }\n}\n\nif (!finalTranslation || finalTranslation.trim().length === 0) {\n  throw new Error('無法獲取翻譯內容，請檢查翻譯節點執行狀態');\n}\n\n// 處理文件名\nif (!originalFileName || originalFileName === 'Unknown') {\n  originalFileName = 'Google文檔';\n}\noriginalFileName = originalFileName.replace(/\\.(doc|docx|txt|pdf)$/i, '');\nconst finalTitle = `${originalFileName}_翻譯`;\n\nconsole.log('📝 Preparing document creation:');\nconsole.log('- Title:', finalTitle);\nconsole.log('- Content length:', finalTranslation.length);\nconsole.log('- Content preview:', finalTranslation.substring(0, 100));\n\n// 返回完整數據，包含翻譯內容\nreturn {\n  title: finalTitle,\n  content: finalTranslation,\n  folderId: '1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg',\n  originalFileName: originalFileName,\n  finalTranslation: finalTranslation,\n  translationContent: finalTranslation\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, -80], "id": "ca4a3976-7817-4fad-b6b1-29f79e36c87e", "name": "Prepare Docs Content"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, -80], "id": "e5415862-da7b-402c-8a39-5b9246994f33", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1440, -80], "id": "9f7866d8-3930-4504-ae50-fa298c9aec99", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "aKNvhMNpGwKLTvmj", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "// 重建 Google Sheets 結構並替換翻譯內容 (修正版)\nconst translatedText = $json.finalTranslation;\nconst originalData = $json.originalData;\n\nif (!originalData) {\n  throw new Error('原始 Sheets 數據缺失，無法重建文件。');\n}\n\n// 將翻譯文本按行分割\nconst translatedLines = translatedText.split('\\n').filter(line => line.trim() !== '');\nlet lineIndex = 0;\n\nconst rebuiltData = [];\n\n// 遍歷原始數據的每一行\nfor (const originalRow of originalData) {\n  // 必須使用 originalRow.json，因為數據被包裹在 json 屬性中\n  const originalRowData = originalRow.json;\n  if (!originalRowData) continue;\n\n  const newRow = {};\n  for (const key in originalRowData) {\n    const originalValue = originalRowData[key];\n    // 僅當原始儲存格是文字且不為空時才進行替換\n    if (typeof originalValue === 'string' && originalValue.trim() !== '') {\n      if (lineIndex < translatedLines.length) {\n        newRow[key] = translatedLines[lineIndex];\n        lineIndex++;\n      } else {\n        // 如果翻譯行數不夠，保留原文以避免錯誤\n        newRow[key] = originalValue;\n      }\n    } else {\n      // 非文字或空儲存格保持原樣\n      newRow[key] = originalValue;\n    }\n  }\n  rebuiltData.push(newRow);\n}\n\nconsole.log(`📊 Rebuilt ${rebuiltData.length} rows for the new sheet.`);\n\n// 直接返回重建後的數據陣列\nreturn rebuiltData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 60], "id": "a514208e-82f8-4ffe-8678-ca0d2f9d8155", "name": "Rebuild Sheets Structure"}, {"parameters": {"operation": "create"}, "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [1360, 60], "id": "26bd34ed-c57d-4389-af96-c7845d6fead9", "name": "Create Excel File"}, {"parameters": {"name": "={{ $('Process Claude Response').item.json.originalFileName.replace(/\\.(xls|xlsx|csv)$/i, '') }}_翻譯", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1560, 60], "id": "bac947a8-0663-4986-8b07-49c631d9fcf3", "name": "Upload Translated Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get the output from the \"Create Google Doc\" node (the current input)\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID from the \"Create Google Doc\" node.');\n}\n\n// Get the output from the \"Prepare Docs Content\" node\n// We use a node reference here because the content data was in the previous step\nconst prepNodeItems = $('Prepare Docs Content').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Prepare Docs Content\" node.');\n}\n\n// Find the content from the first item of that node's output\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Prepare Docs Content\" node.');\n}\n\n// Return the combined payload\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1240, -80], "id": "38abc06d-b15a-4f1a-8fe7-47d305181aa5", "name": "Prepare Final Payload"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Download Google Sheets as Excel", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Download Google Sheets as Excel": {"main": [[{"node": "Parse Sheets Content", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Parse Sheets Content": {"main": [[{"node": "Extract Sheets Text", "type": "main", "index": 0}]]}, "Extract Sheets Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 1}]]}, "Merge Extracted Content": {"main": [[{"node": "First Translation (<PERSON>)", "type": "main", "index": 0}]]}, "First Translation (Gemini)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>)", "type": "main", "index": 0}]]}, "Reflective Translation (Claude)": {"main": [[{"node": "Process Claude Response", "type": "main", "index": 0}]]}, "Process Claude Response": {"main": [[{"node": "Output Type Switch", "type": "main", "index": 0}]]}, "Output Type Switch": {"main": [[{"node": "Prepare Docs Content", "type": "main", "index": 0}], [{"node": "Rebuild Sheets Structure", "type": "main", "index": 0}]]}, "Rebuild Sheets Structure": {"main": [[{"node": "Create Excel File", "type": "main", "index": 0}]]}, "Create Excel File": {"main": [[{"node": "Upload Translated Sheet", "type": "main", "index": 0}]]}, "Prepare Docs Content": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "81c58287-ffb1-457d-89e1-501cbad48790", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}