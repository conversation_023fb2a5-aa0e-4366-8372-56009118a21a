#!/usr/bin/env node

/**
 * N8N 翻譯工作流程依賴庫測試腳本
 * 
 * 此腳本用於驗證 mammoth 和 xlsx 庫是否正確安裝
 * 使用方法: node test-libraries.js
 */

console.log('🔍 開始檢查 N8N 翻譯工作流程所需的依賴庫...\n');

// 測試 mammoth.js
console.log('📄 測試 mammoth.js (Word 檔案解析庫)...');
try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 已成功安裝');
  console.log(`   版本: ${mammoth.version || '未知'}`);
  
  // 測試基本功能
  if (typeof mammoth.extractRawText === 'function') {
    console.log('✅ mammoth.extractRawText 功能可用');
  } else {
    console.log('⚠️  mammoth.extractRawText 功能不可用');
  }
} catch (error) {
  console.log('❌ mammoth.js 未安裝或安裝失敗');
  console.log(`   錯誤: ${error.message}`);
  console.log('   安裝方法: npm install mammoth');
}

console.log('');

// 測試 xlsx
console.log('📊 測試 xlsx (Excel 檔案解析庫)...');
try {
  const XLSX = require('xlsx');
  console.log('✅ xlsx 已成功安裝');
  console.log(`   版本: ${XLSX.version || '未知'}`);
  
  // 測試基本功能
  if (typeof XLSX.read === 'function') {
    console.log('✅ XLSX.read 功能可用');
  } else {
    console.log('⚠️  XLSX.read 功能不可用');
  }
  
  if (typeof XLSX.utils === 'object' && typeof XLSX.utils.sheet_to_json === 'function') {
    console.log('✅ XLSX.utils.sheet_to_json 功能可用');
  } else {
    console.log('⚠️  XLSX.utils.sheet_to_json 功能不可用');
  }
} catch (error) {
  console.log('❌ xlsx 未安裝或安裝失敗');
  console.log(`   錯誤: ${error.message}`);
  console.log('   安裝方法: npm install xlsx');
}

console.log('');

// 檢查 Node.js 版本
console.log('🔧 檢查 Node.js 環境...');
console.log(`✅ Node.js 版本: ${process.version}`);
console.log(`✅ 平台: ${process.platform}`);
console.log(`✅ 架構: ${process.arch}`);

console.log('');

// 總結
console.log('📋 檢查總結:');
let mammothOk = false;
let xlsxOk = false;

try {
  require('mammoth');
  mammothOk = true;
  console.log('✅ mammoth.js: 可用');
} catch {
  console.log('❌ mammoth.js: 不可用');
}

try {
  require('xlsx');
  xlsxOk = true;
  console.log('✅ xlsx: 可用');
} catch {
  console.log('❌ xlsx: 不可用');
}

console.log('');

if (mammothOk && xlsxOk) {
  console.log('🎉 所有依賴庫都已正確安裝！N8N 翻譯工作流程可以正常運行。');
  process.exit(0);
} else {
  console.log('⚠️  部分依賴庫未安裝。請按照以下步驟安裝：');
  console.log('');
  
  if (!mammothOk) {
    console.log('📄 安裝 mammoth.js:');
    console.log('   npm install mammoth');
  }
  
  if (!xlsxOk) {
    console.log('📊 安裝 xlsx:');
    console.log('   npm install xlsx');
  }
  
  console.log('');
  console.log('安裝完成後，請重啟 N8N 服務。');
  process.exit(1);
}
