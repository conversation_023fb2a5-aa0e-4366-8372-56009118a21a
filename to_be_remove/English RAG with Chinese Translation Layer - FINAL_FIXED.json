{"name": "English RAG with Chinese Translation Layer - FINAL_FIXED", "nodes": [{"parameters": {}, "name": "RAG Pipeline Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, -100]}, {"parameters": {"authentication": "oAuth2", "pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "fileCreatedOrUpdated", "folderId": "151qVCX7FeAl90L1rIUd4CNyBj0puJI1s"}, "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [450, -100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType || ''}}", "operation": "equal", "value2": "application/pdf"}]}}, "name": "Check if PDF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, -100]}, {"parameters": {"conditions": {"string": [{"value1": "={{($json.mimeType || '').toLowerCase()}}", "operation": "contains", "value2": "presentation"}, {"value1": "={{($json.mimeType || '').toLowerCase()}}", "operation": "contains", "value2": "powerpoint"}], "combinator": "or"}}, "name": "Check if PPT", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 0]}, {"parameters": {"conditions": {"string": [{"value1": "={{($json.mimeType || '').toLowerCase()}}", "operation": "contains", "value2": "word"}]}}, "name": "Check if DOC", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 100]}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}"}, "name": "Download PDF", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, -100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "txt"}}}}, "name": "Download PPT as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, 0], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "txt"}}}}, "name": "Download DOC as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, 100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Extract text from PDF binary data using pdf-parse\nconst pdfParse = require('pdf-parse');\n\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const buffer = Buffer.from(item.binary.data.data);\n      const pdfData = await pdfParse(buffer);\n      \n      item.json.extractedText = pdfData.text;\n      item.json.pageCount = pdfData.numpages;\n      item.json.fileType = 'PDF';\n    } catch (error) {\n      item.json.extractedText = 'Error extracting PDF text: ' + error.message;\n      item.json.fileType = 'PDF';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract PDF Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, -100]}, {"parameters": {"jsCode": "// Process PPT text content\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n      item.json.extractedText = textContent;\n      item.json.fileType = 'PPT';\n    } catch (error) {\n      item.json.extractedText = 'Error processing PPT: ' + error.message;\n      item.json.fileType = 'PPT';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract PPT Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, 0]}, {"parameters": {"jsCode": "// Process DOC text content\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n      item.json.extractedText = textContent;\n      item.json.fileType = 'DOC';\n    } catch (error) {\n      item.json.extractedText = 'Error processing DOC: ' + error.message;\n      item.json.fileType = 'DOC';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract DOC Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, 100]}, {"parameters": {"jsCode": "// 合併來自不同文件類型的提取內容\nconst allItems = $input.all();\n\n// 如果沒有任何輸入，返回空\nif (allItems.length === 0) {\n  return [];\n}\n\n// 取第一個有效的項目作為基礎\nconst mergedItem = allItems[0];\n\n// 確保有提取的文本內容\nif (!mergedItem.json.extractedText) {\n  mergedItem.json.extractedText = \"No content extracted\";\n  mergedItem.json.fileType = \"Unknown\";\n}\n\n// 添加處理時間戳\nmergedItem.json.processedAt = new Date().toISOString();\n\n// 返回合併後的單一項目\nreturn [mergedItem];"}, "name": "Merge Extracted Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1250, 0]}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs", "options": {"googleFileConversion": {"conversion": {"sheetsToFormat": "csv"}}}}, "name": "Load Glossary Sheet", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [450, 150], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Parse CSV glossary data into JSON object\nconst items = $input.all();\nconst glossaryMap = {};\n\nfor (const item of items) {\n  if (item.binary && item.binary.data) {\n    const csvContent = Buffer.from(item.binary.data.data).toString('utf8');\n    const lines = csvContent.split('\\n');\n    \n    // Skip header row, process data rows\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (line) {\n        const [english, chinese] = line.split(',').map(cell => cell.replace(/\"/g, '').trim());\n        if (english && chinese) {\n          glossaryMap[english] = chinese;\n        }\n      }\n    }\n  }\n}\n\nreturn [{ json: { glossary: glossaryMap } }];", "onError": "continueRegularOutput"}, "name": "Parse Glossary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [650, 150]}, {"parameters": {"model": "text-embedding-3-small", "inputText": "={{$json.extractedText}}"}, "name": "Generate Document Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1450, 0], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"action": "insert", "table": "documents", "columns": [{"name": "content", "value": "={{$json.extractedText}}"}, {"name": "filename", "value": "={{$json.name}}"}, {"name": "file_type", "value": "={{$json.fileType}}"}, {"name": "metadata", "value": "={{JSON.stringify($json)}}"}, {"name": "embedding", "value": "={{JSON.stringify($node['Generate Document Embedding'].json.data[0].embedding)}}"}]}, "name": "Insert to Superbase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1650, 0], "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"path": "webhook-agent", "options": {}}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"jsCode": "// 智能查詢複雜度分析 + 成本優化\nconst query = $input.all()[0].json.body?.message || '';\n\n// 複雜度判斷邏輯\nconst complexKeywords = ['API', 'workflow', 'configuration', 'integration', 'architecture', 'technical', 'implement', 'best practice', '如何配置', '怎麼實現', '詳細說明', '深入分析'];\nconst simpleKeywords = ['what is', 'define', 'meaning', '是什麼', '簡單介紹', '定義', '簡述'];\n\nlet complexity = 'complex'; // 預設複雜\n\n// 長度判斷\nif (query.length < 50) {\n  complexity = 'simple';\n}\n\n// 關鍵詞匹配\nconst hasComplexKeywords = complexKeywords.some(keyword => \n  query.toLowerCase().includes(keyword.toLowerCase())\n);\nconst hasSimpleKeywords = simpleKeywords.some(keyword => \n  query.toLowerCase().includes(keyword.toLowerCase())\n);\n\nif (hasComplexKeywords) complexity = 'complex';\nif (hasSimpleKeywords) complexity = 'simple';\n\n// 記錄查詢信息用於成本追蹤\nconst timestamp = new Date().toISOString();\nconst queryInfo = {\n  query: query,\n  complexity: complexity,\n  timestamp: timestamp,\n  source: 'webhook'\n};\n\nreturn [{\n  json: {\n    ...queryInfo,\n    originalData: $input.all()[0].json\n  }\n}];"}, "name": "Smart Query Analyzer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [650, 300]}, {"parameters": {"model": "text-embedding-3-small", "inputText": "={{$json.query}}"}, "name": "Generate Query Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [850, 300], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM search_documents_by_vector('[{{$node[\"Generate Query Embedding\"].json.data[0].embedding.join(',')}}]'::vector, 0.3, 5)"}, "name": "Vector Search Superbase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1050, 300], "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.complexity }}", "operation": "equal", "value2": "simple"}]}}, "name": "Route by Complexity", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"model": "gpt-4o-mini", "chatMessages": {"messages": [{"role": "system", "text": "You are a helpful assistant. Answer the user's question based on the provided context. The context is retrieved from a knowledge base. If the context does not contain the answer, state that you cannot find the information in the documents. Answer only in English. Keep responses concise for simple queries."}, {"role": "user", "text": "Context:\\n---\\n{{$node[\"Vector Search Superbase\"].json.map(item => item.content).join('\\n---\\n')}}\\n---\\n\\nQuestion: {{$node[\"Smart Query Analyzer\"].json.query}}\\n\\nSimilarity Scores: {{$node[\"Vector Search Superbase\"].json.map(item => 'Score: ' + (item.similarity || 'N/A')).join(', ')}}"}]}}, "name": "OpenAI Mini (Simple)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1450, 200], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"model": "gpt-4-turbo", "chatMessages": {"messages": [{"role": "system", "text": "You are an expert assistant. Answer the user's question based on the provided context with detailed analysis. The context is retrieved from a knowledge base. If the context does not contain the answer, state that you cannot find the information in the documents. Answer only in English. Provide comprehensive responses for complex queries."}, {"role": "user", "text": "Context:\\n---\\n{{$node[\"Vector Search Superbase\"].json.map(item => item.content).join('\\n---\\n')}}\\n---\\n\\nQuestion: {{$node[\"Smart Query Analyzer\"].json.query}}\\n\\nSimilarity Scores: {{$node[\"Vector Search Superbase\"].json.map(item => 'Score: ' + (item.similarity || 'N/A')).join(', ')}}"}]}}, "name": "OpenAI GPT-4 (Complex)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1450, 400], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// 智能結果合併\nconst simpleResult = $('OpenAI Mini (Simple)').length > 0 ? $('OpenAI Mini (Simple)').first().json : null;\nconst complexResult = $('OpenAI GPT-4 (Complex)').length > 0 ? $('OpenAI GPT-4 (Complex)').first().json : null;\n\n// 選擇實際執行的結果\nconst result = simpleResult || complexResult;\nconst engineUsed = simpleResult ? 'gpt-4o-mini' : 'gpt-4-turbo';\n\n// 獲取原始查詢信息\nconst queryInfo = $('Smart Query Analyzer').first().json;\n\n// 成本估算\nconst estimateCost = (engine, query, response) => {\n  const pricing = {\n    'gpt-4-turbo': { input: 0.01, output: 0.03 },\n    'gpt-4o-mini': { input: 0.00015, output: 0.0006 },\n    'text-embedding-3-small': { input: 0.00002, output: 0 }\n  };\n  \n  const inputTokens = Math.ceil(query.length / 4) + 200; // 系統提示 + 上下文\n  const outputTokens = Math.ceil(response.length / 4);\n  const embeddingTokens = Math.ceil(query.length / 4);\n  \n  const price = pricing[engine];\n  const ragCost = ((inputTokens * price.input) + (outputTokens * price.output)) / 1000;\n  const embeddingCost = (embeddingTokens * pricing['text-embedding-3-small'].input) / 1000;\n  \n  return ragCost + embeddingCost;\n};\n\nconst responseText = result.choices[0].message.content;\nconst estimatedCost = estimateCost(engineUsed, queryInfo.query, responseText);\n\nreturn [{\n  json: {\n    englishAnswer: responseText,\n    engineUsed: engineUsed,\n    complexity: queryInfo.complexity,\n    query: queryInfo.query,\n    timestamp: queryInfo.timestamp,\n    originalData: queryInfo.originalData,\n    costTracking: {\n      ragEngine: engineUsed,\n      estimatedCost: estimatedCost,\n      timestamp: queryInfo.timestamp,\n      complexity: queryInfo.complexity\n    }\n  }\n}];"}, "name": "Smart Result Merger", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1650, 300]}, {"parameters": {"model": "gpt-4-turbo", "chatMessages": {"messages": [{"role": "system", "text": "你是專業的英翻中翻譯專家，專精於英文到繁體中文的翻譯。你必須嚴格遵循提供的自訂詞彙表進行特定術語的翻譯。對於詞彙表中沒有的詞彙，請使用你的專業判斷提供自然、準確的繁體中文翻譯。"}, {"role": "user", "text": "請將以下英文文本翻譯成繁體中文。\\n\\n**重要指示：**\\n1. 你必須使用下方自訂詞彙表中的確切翻譯\\n2. 不要偏離詞彙表的翻譯\\n3. 對於詞彙表中沒有的術語，使用自然的繁體中文\\n4. 保持原文的意思和語境\\n\\n**自訂詞彙表 (英文 → 繁體中文)：**\\n---\\n{{$('Parse Glossary').length > 0 ? JSON.stringify($('Parse Glossary').first().json.glossary, null, 2) : '無詞彙表載入'}}\\n---\\n\\n**要翻譯的英文文本：**\\n{{$json.englishAnswer}}"}]}}, "name": "Translate to Chinese", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1850, 300], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"answer\": $json.choices[0].message.content,\n  \"english_answer\": $('Smart Result Merger').first().json.englishAnswer,\n  \"cost_info\": $('Smart Result Merger').first().json.costTracking\n} }}"}, "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2050, 300]}], "connections": {"Google Drive Trigger": {"main": [[{"node": "Check if PDF", "type": "main", "index": 0}, {"node": "Check if PPT", "type": "main", "index": 0}, {"node": "Check if DOC", "type": "main", "index": 0}]]}, "Check if PDF": {"main": [[{"node": "Download PDF", "type": "main", "index": 0}]]}, "Check if PPT": {"main": [[{"node": "Download PPT as Text", "type": "main", "index": 0}]]}, "Check if DOC": {"main": [[{"node": "Download DOC as Text", "type": "main", "index": 0}]]}, "Download PDF": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}]]}, "Download PPT as Text": {"main": [[{"node": "Extract PPT Text", "type": "main", "index": 0}]]}, "Download DOC as Text": {"main": [[{"node": "Extract DOC Text", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Extract PPT Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Extract DOC Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Merge Extracted Content": {"main": [[{"node": "Generate Document Embedding", "type": "main", "index": 0}]]}, "Generate Document Embedding": {"main": [[{"node": "Insert to Superbase", "type": "main", "index": 0}]]}, "RAG Pipeline Start": {"main": [[{"node": "Load Glossary Sheet", "type": "main", "index": 0}]]}, "Load Glossary Sheet": {"main": [[{"node": "Parse Glossary", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Smart Query Analyzer", "type": "main", "index": 0}]]}, "Smart Query Analyzer": {"main": [[{"node": "Generate Query Embedding", "type": "main", "index": 0}]]}, "Generate Query Embedding": {"main": [[{"node": "Vector Search Superbase", "type": "main", "index": 0}]]}, "Vector Search Superbase": {"main": [[{"node": "Route by Complexity", "type": "main", "index": 0}]]}, "Route by Complexity": {"main": [[{"node": "OpenAI Mini (Simple)", "type": "main", "index": 0}], [{"node": "OpenAI GPT-4 (Complex)", "type": "main", "index": 0}]]}, "OpenAI Mini (Simple)": {"main": [[{"node": "Smart Result Merger", "type": "main", "index": 0}]]}, "OpenAI GPT-4 (Complex)": {"main": [[{"node": "Smart Result Merger", "type": "main", "index": 0}]]}, "Smart Result Merger": {"main": [[{"node": "Translate to Chinese", "type": "main", "index": 0}]]}, "Translate to Chinese": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}}