{"name": "English RAG with Chinese Translation Layer", "nodes": [{"parameters": {}, "name": "RAG Pipeline Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, -100]}, {"parameters": {"authentication": "oAuth2", "triggerOn": "fileCreatedOrUpdated", "folderId": "151qVCX7FeAl90L1rIUd4CNyBj0puJI1s", "options": {"fileTypes": ["application/pdf", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]}}, "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [450, -100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "operation": "equal", "value2": "application/pdf"}]}}, "name": "Check if PDF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, -100]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "operation": "contains", "value2": "presentation"}]}}, "name": "Check if PPT", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 0]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "operation": "contains", "value2": "word"}]}}, "name": "Check if DOC", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 100]}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}"}, "name": "Download PDF", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, -100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "txt"}}}}, "name": "Download PPT as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, 0], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "txt"}}}}, "name": "Download DOC as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [850, 100], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Extract text from PDF binary data using pdf-parse\nconst pdfParse = require('pdf-parse');\n\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const buffer = Buffer.from(item.binary.data.data);\n      const pdfData = await pdfParse(buffer);\n      \n      item.json.extractedText = pdfData.text;\n      item.json.pageCount = pdfData.numpages;\n      item.json.fileType = 'PDF';\n    } catch (error) {\n      item.json.extractedText = 'Error extracting PDF text: ' + error.message;\n      item.json.fileType = 'PDF';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract PDF Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, -100]}, {"parameters": {"jsCode": "// Process PPT text content\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n      item.json.extractedText = textContent;\n      item.json.fileType = 'PPT';\n    } catch (error) {\n      item.json.extractedText = 'Error processing PPT: ' + error.message;\n      item.json.fileType = 'PPT';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract PPT Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, 0]}, {"parameters": {"jsCode": "// Process DOC text content\nfor (const item of $input.all()) {\n  if (item.binary && item.binary.data) {\n    try {\n      const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n      item.json.extractedText = textContent;\n      item.json.fileType = 'DOC';\n    } catch (error) {\n      item.json.extractedText = 'Error processing DOC: ' + error.message;\n      item.json.fileType = 'DOC';\n    }\n  }\n}\n\nreturn $input.all();"}, "name": "Extract DOC Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1050, 100]}, {"parameters": {"mode": "combine", "combineBy": "combineAll"}, "name": "Merge Extracted Content", "type": "n8n-nodes-base.itemLists", "typeVersion": 3, "position": [1250, 0]}, {"parameters": {"authentication": "oAuth2", "operation": "download", "fileId": "1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs", "options": {"googleFileConversion": {"conversion": {"sheetsToFormat": "csv"}}}}, "name": "Load Glossary Sheet", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [450, 150], "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Parse CSV glossary data into JSON object\nconst items = $input.all();\nconst glossaryMap = {};\n\nfor (const item of items) {\n  if (item.binary && item.binary.data) {\n    const csvContent = Buffer.from(item.binary.data.data).toString('utf8');\n    const lines = csvContent.split('\\n');\n    \n    // Skip header row, process data rows\n    for (let i = 1; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (line) {\n        const [english, chinese] = line.split(',').map(cell => cell.replace(/\"/g, '').trim());\n        if (english && chinese) {\n          glossaryMap[english] = chinese;\n        }\n      }\n    }\n  }\n}\n\nreturn [{ json: { glossary: glossaryMap } }];", "onError": "continueRegularOutput"}, "name": "Parse Glossary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [650, 150]}, {"parameters": {"model": "text-embedding-3-small", "inputText": "={{$json.extractedText}}", "options": {}}, "name": "Generate Document Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1250, 0], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"action": "insert", "table": "documents", "columns": [{"name": "content", "value": "={{$json.extractedText}}"}, {"name": "filename", "value": "={{$json.name}}"}, {"name": "file_type", "value": "={{$json.fileType}}"}, {"name": "metadata", "value": "={{JSON.stringify($json)}}"}, {"name": "embedding", "value": "={{JSON.stringify($node['Generate Document Embedding'].json.data[0].embedding)}}"}]}, "name": "Insert to Superbase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1450, 0], "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"path": "webhook-agent", "options": {}}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"model": "gpt-4-turbo", "chatMessages": {"messages": [{"role": "system", "text": "You are a helpful assistant. Answer the user's question based on the provided context. The context is retrieved from a knowledge base. If the context does not contain the answer, state that you cannot find the information in the documents. Answer only in English."}, {"role": "user", "text": "Context:\n---\n{{$node[\"Vector Search Superbase\"].json.map(item => item.content).join('\\n---\\n')}}\n---\n\nQuestion: {{$node[\"Line Trigger\"].json.events[0].message.text || $node[\"Webhook Trigger\"].json.body.message}}\n\nSimilarity Scores: {{$node[\"Vector Search Superbase\"].json.map(item => 'Score: ' + (item.similarity || 'N/A')).join(', ')}}"}]}, "options": {}}, "name": "OpenAI (Generate English Answer)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [950, 450], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"model": "text-embedding-3-small", "inputText": "={{$node[\"Line Trigger\"].json.events[0].message.text || $node[\"Webhook Trigger\"].json.body.message}}", "options": {}}, "name": "Generate Query Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [550, 450], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM search_documents_by_vector('[{{$node[\"Generate Query Embedding\"].json.data[0].embedding.join(',')}}]'::vector, 0.3, 5)"}, "name": "Vector Search Superbase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [750, 450], "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"events": ["message"]}, "name": "Line Trigger", "type": "n8n-nodes-base.lineTrigger", "typeVersion": 1, "position": [450, 500], "credentials": {"lineApi": {"id": "YOUR_LINE_CREDENTIALS_ID", "name": "Line credentials"}}}, {"parameters": {"operation": "replyMessage", "replyToken": "={{$node[\"Line Trigger\"].json.events[0].replyToken}}", "messages": {"messages": [{"type": "text", "text": "={{$node[\"Translate to Chinese\"].json.choices[0].message.content}}"}]}}, "name": "Line (Send Reply)", "type": "n8n-nodes-base.line", "typeVersion": 1, "position": [1250, 500], "credentials": {"lineApi": {"id": "YOUR_LINE_CREDENTIALS_ID", "name": "Line credentials"}}}, {"parameters": {"model": "gpt-4-turbo", "chatMessages": {"messages": [{"role": "system", "text": "You are an expert translator specializing in English to Traditional Chinese (繁體中文) translation. You must strictly follow the provided custom glossary for specific terms. For terms not in the glossary, use your best judgment to provide natural, accurate Traditional Chinese translations."}, {"role": "user", "text": "Please translate the following English text into Traditional Chinese (繁體中文).\n\n**CRITICAL INSTRUCTIONS:**\n1. You MUST use the exact translations from the custom glossary below for any matching terms\n2. Do NOT deviate from the glossary translations\n3. For terms not in the glossary, use natural Traditional Chinese\n4. Maintain the original meaning and context\n\n**Custom Glossary (English → Traditional Chinese):**\n---\n{{$('Parse Glossary').length > 0 ? JSON.stringify($('Parse Glossary').first().json.glossary, null, 2) : 'No glossary loaded'}}\n---\n\n**English text to translate:**\n{{$node[\"OpenAI (Generate English Answer)\"].json.choices[0].message.content}}"}]}, "options": {}}, "name": "Translate to Chinese", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1050, 450], "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}], "connections": {"Google Drive Trigger": {"main": [[{"node": "Check if PDF", "type": "main", "index": 0}, {"node": "Check if PPT", "type": "main", "index": 0}, {"node": "Check if DOC", "type": "main", "index": 0}]]}, "Check if PDF": {"main": [[{"node": "Download PDF", "type": "main", "index": 0}]]}, "Check if PPT": {"main": [[{"node": "Download PPT as Text", "type": "main", "index": 0}]]}, "Check if DOC": {"main": [[{"node": "Download DOC as Text", "type": "main", "index": 0}]]}, "Download PDF": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}]]}, "Download PPT as Text": {"main": [[{"node": "Extract PPT Text", "type": "main", "index": 0}]]}, "Download DOC as Text": {"main": [[{"node": "Extract DOC Text", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Extract PPT Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Extract DOC Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Merge Extracted Content": {"main": [[{"node": "Generate Document Embedding", "type": "main", "index": 0}]]}, "Generate Document Embedding": {"main": [[{"node": "Insert to Superbase", "type": "main", "index": 0}]]}, "RAG Pipeline Start": {"main": [[{"node": "Load Glossary Sheet", "type": "main", "index": 0}]]}, "Load Glossary Sheet": {"main": [[{"node": "Parse Glossary", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Generate Query Embedding", "type": "main", "index": 0}]]}, "Generate Query Embedding": {"main": [[{"node": "Vector Search Superbase", "type": "main", "index": 0}]]}, "OpenAI (Generate English Answer)": {"main": [[{"node": "Translate to Chinese", "type": "main", "index": 0}]]}, "Vector Search Superbase": {"main": [[{"node": "OpenAI (Generate English Answer)", "type": "main", "index": 0}]]}, "Line Trigger": {"main": [[{"node": "Generate Query Embedding", "type": "main", "index": 0}]]}, "Translate to Chinese": {"main": [[{"node": "Line (Send Reply)", "type": "main", "index": 0}]]}}}