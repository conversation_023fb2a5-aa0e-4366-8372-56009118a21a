// 簡化的 Word 轉換測試節點
const item = $input.item;

console.log('🔍 Word Convert Test:');
console.log('  - Input data:', JSON.stringify(item.json, null, 2));
console.log('  - File ID:', item.json.id);
console.log('  - File name:', item.json.name);
console.log('  - MIME type:', item.json.mimeType);

// 檢查必要的數據
if (!item.json.id) {
  throw new Error('Missing file ID');
}

if (!item.json.name) {
  throw new Error('Missing file name');
}

// 準備轉換請求
const convertRequest = {
  url: `https://www.googleapis.com/drive/v3/files/${item.json.id}/copy`,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: {
    name: item.json.name + '_temp_conversion',
    mimeType: 'application/vnd.google-apps.document'
  }
};

console.log('📤 Convert request:', JSON.stringify(convertRequest, null, 2));

// 返回原始數據以便後續節點使用
return item.json;
