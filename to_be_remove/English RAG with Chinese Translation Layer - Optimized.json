{"name": "My workflow 4", "nodes": [{"parameters": {}, "name": "RAG Pipeline Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [-860, 160], "id": "007ba0ff-9222-45cc-9c29-2b429d3e3350"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "fileCreatedOrUpdated"}, "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-660, 160], "id": "580333b8-d58d-4123-aeb5-337bc5f990a3", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"options": {}}, "name": "Get File Content", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [-460, 160], "id": "ee700859-2083-4332-aa04-3d4a51d82792"}, {"parameters": {"tableId": "documents"}, "name": "Insert to Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-260, 160], "id": "0a443848-26be-48d0-8841-81c73f6878bc", "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"path": "webhook-agent", "options": {}}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-860, 560], "id": "552b451d-2f15-47d3-b9c1-9362e1e42bb1", "webhookId": "b531ffc3-1886-4352-9aef-0c3754a1d554"}, {"parameters": {}, "name": "Line Trigger", "type": "n8n-nodes-base.lineTrigger", "typeVersion": 1, "position": [-860, 760], "id": "44a97ca1-e4d6-4825-ace5-d4d82bda5a3b", "credentials": {}}, {"parameters": {"jsCode": "// 智能查询复杂度分析\nconst query = $input.all()[0].json.body?.message || $input.all()[0].json.events[0]?.message?.text || '';\n\n// 复杂度判断逻辑\nconst complexKeywords = ['API', 'workflow', 'configuration', 'integration', 'architecture', 'technical', 'implement', 'best practice', '如何配置', '怎么实现'];\nconst simpleKeywords = ['what is', 'define', 'meaning', '是什么', '简单介绍', '定义'];\n\nlet complexity = 'complex'; // 默认复杂\n\n// 长度判断\nif (query.length < 50) {\n  complexity = 'simple';\n}\n\n// 关键词匹配\nconst hasComplexKeywords = complexKeywords.some(keyword => \n  query.toLowerCase().includes(keyword.toLowerCase())\n);\nconst hasSimpleKeywords = simpleKeywords.some(keyword => \n  query.toLowerCase().includes(keyword.toLowerCase())\n);\n\nif (hasComplexKeywords) complexity = 'complex';\nif (hasSimpleKeywords) complexity = 'simple';\n\n// 记录查询信息用于成本跟踪\nconst timestamp = new Date().toISOString();\nconst queryInfo = {\n  query: query,\n  complexity: complexity,\n  timestamp: timestamp,\n  source: $input.all()[0].json.events ? 'line' : 'webhook'\n};\n\nreturn [{\n  json: {\n    ...queryInfo,\n    originalData: $input.all()[0].json\n  }\n}];"}, "name": "Query Complexity Analyzer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-660, 660], "id": "42d665a8-1ba9-47c3-81ca-4fd34715808d"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "simple_query", "leftValue": "={{ $json.complexity }}", "rightValue": "simple", "operator": {"operation": "equals"}}], "combinator": "and"}, "options": {}}, "name": "Route by Complexity", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-460, 660], "id": "557e20b1-534b-4bfe-9c8d-c3b68572e49b"}, {"parameters": {"tableId": "documents"}, "name": "Search Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-260, 560], "id": "b5281512-e236-4536-98ee-476a1eeddd6a", "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"model": "gpt-4o-mini", "options": {}, "requestOptions": {}}, "name": "OpenAI Mini (Simple Queries)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [-60, 520], "id": "fc12d365-81ff-4f8e-8515-5beb81cc5023", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"model": "gpt-4-turbo", "options": {}, "requestOptions": {}}, "name": "OpenAI GPT-4 (Complex Queries)", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [-60, 820], "id": "cb36a7f1-576b-4e81-b7a7-781b1321c6b3", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// 合并来自不同RAG引擎的结果\nconst simpleResult = $('OpenAI Mini (Simple Queries)').length > 0 ? $('OpenAI Mini (Simple Queries)').first().json : null;\nconst complexResult = $('OpenAI GPT-4 (Complex Queries)').length > 0 ? $('OpenAI GPT-4 (Complex Queries)').first().json : null;\n\n// 选择实际执行的结果\nconst result = simpleResult || complexResult;\nconst engineUsed = simpleResult ? 'gpt-4o-mini' : 'gpt-4-turbo';\n\n// 获取原始查询信息\nconst queryInfo = $input.all()[0].json;\n\nreturn [{\n  json: {\n    englishAnswer: result.choices[0].message.content,\n    engineUsed: engineUsed,\n    complexity: queryInfo.complexity,\n    query: queryInfo.query,\n    timestamp: queryInfo.timestamp,\n    originalData: queryInfo.originalData\n  }\n}];"}, "name": "Merge <PERSON>G Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [160, 660], "id": "4b9d7a41-f3fc-4a59-ab8b-a2326ee391a8"}, {"parameters": {}, "name": "Claude 3.5 Translate", "type": "n8n-nodes-base.anthropic", "typeVersion": 1, "position": [360, 660], "id": "8d457fbd-7c31-4ae7-9908-5764a165a5ba", "credentials": {}}, {"parameters": {"jsCode": "// 解析Claude回应并添加成本跟踪信息\nconst claudeResponse = $input.all()[0].json;\nconst translatedText = claudeResponse.content[0].text;\n\n// 获取之前的查询信息\nconst queryInfo = $input.all()[0].json;\n\n// 成本估算（基于token使用）\nconst estimateCost = (engine, inputTokens, outputTokens) => {\n  const pricing = {\n    'gpt-4-turbo': { input: 0.01, output: 0.03 },\n    'gpt-4o-mini': { input: 0.00015, output: 0.0006 },\n    'claude-3.5-sonnet': { input: 0.003, output: 0.015 }\n  };\n  \n  const price = pricing[engine] || pricing['gpt-4-turbo'];\n  return ((inputTokens * price.input) + (outputTokens * price.output)) / 1000;\n};\n\n// 估算token数（粗略估算：英文1token≈4字符，中文1token≈1.5字符）\nconst inputTokens = Math.ceil(queryInfo.query.length / 4) + 100; // 加上系统提示\nconst outputTokens = Math.ceil(translatedText.length / 1.5);\n\nconst ragCost = estimateCost(queryInfo.engineUsed, inputTokens, outputTokens * 0.7);\nconst translateCost = estimateCost('claude-3.5-sonnet', outputTokens * 0.7, outputTokens);\nconst totalCost = ragCost + translateCost;\n\nreturn [{\n  json: {\n    translatedAnswer: translatedText,\n    originalData: queryInfo.originalData,\n    costTracking: {\n      ragEngine: queryInfo.engineUsed,\n      translateEngine: 'claude-3.5-sonnet',\n      complexity: queryInfo.complexity,\n      estimatedCost: totalCost,\n      timestamp: queryInfo.timestamp\n    },\n    replyToken: queryInfo.originalData.events ? queryInfo.originalData.events[0].replyToken : null\n  }\n}];"}, "name": "Process Final Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [560, 660], "id": "72d5f3a4-417f-4f82-bfb1-42c9050d0aeb"}, {"parameters": {"operation": "replyMessage"}, "name": "Line Send Reply", "type": "n8n-nodes-base.line", "typeVersion": 1, "position": [760, 760], "id": "1544ea87-4537-4bac-b150-1a3d66b2a4e3"}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"answer\": $json.translatedAnswer,\n  \"cost_info\": $json.costTracking\n} }}", "options": {}}, "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [760, 560], "id": "01fc73a0-e840-4783-8d2b-f517cafea83e"}, {"parameters": {"tableId": "documents"}, "name": "Log Cost Tracking", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [560, 860], "id": "1f3680fd-c2d1-4202-8963-29e4a55ef43e", "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Get File Content", "type": "main", "index": 0}]]}, "Get File Content": {"main": [[{"node": "Insert to Supabase", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Query Complexity Analyzer", "type": "main", "index": 0}]]}, "Query Complexity Analyzer": {"main": [[{"node": "Route by Complexity", "type": "main", "index": 0}]]}, "Route by Complexity": {"main": [[{"node": "Search Supabase", "type": "main", "index": 0}], [{"node": "Search Supabase", "type": "main", "index": 0}]]}, "Search Supabase": {"main": [[{"node": "OpenAI Mini (Simple Queries)", "type": "main", "index": 0}, {"node": "OpenAI GPT-4 (Complex Queries)", "type": "main", "index": 0}, {"node": "Merge <PERSON>G Results", "type": "main", "index": 0}]]}, "OpenAI Mini (Simple Queries)": {"main": [[{"node": "Merge <PERSON>G Results", "type": "main", "index": 0}]]}, "OpenAI GPT-4 (Complex Queries)": {"main": [[{"node": "Merge <PERSON>G Results", "type": "main", "index": 0}]]}, "Process Final Response": {"main": [[{"node": "Line Send Reply", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}, {"node": "Log Cost Tracking", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "b11fad4f-c36e-47f8-ad51-6697d4b3769b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}