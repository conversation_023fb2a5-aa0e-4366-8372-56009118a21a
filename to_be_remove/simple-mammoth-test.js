// 簡單的 mammoth.js 測試
// 在 N8N Code 節點中運行此代碼

console.log('🧪 開始 mammoth.js 簡單測試...');

// 測試 1: 基本載入
try {
  const mammoth = require('mammoth');
  console.log('✅ mammoth.js 基本載入成功');
  console.log('mammoth 對象類型:', typeof mammoth);
  console.log('可用方法:', Object.keys(mammoth));
  
  // 測試 2: 檢查關鍵方法
  if (typeof mammoth.extractRawText === 'function') {
    console.log('✅ extractRawText 方法可用');
  } else {
    console.log('❌ extractRawText 方法不可用');
  }
  
  // 測試 3: 創建一個簡單的測試 Buffer
  const testBuffer = Buffer.from('test');
  console.log('✅ Buffer 創建成功');
  
  return {
    status: 'success',
    mammoth_available: true,
    methods: Object.keys(mammoth),
    test_time: new Date().toISOString()
  };
  
} catch (error) {
  console.log('❌ mammoth.js 測試失敗');
  console.log('錯誤詳情:', error.message);
  console.log('錯誤堆疊:', error.stack);
  
  return {
    status: 'failed',
    mammoth_available: false,
    error: error.message,
    error_code: error.code,
    test_time: new Date().toISOString()
  };
}
