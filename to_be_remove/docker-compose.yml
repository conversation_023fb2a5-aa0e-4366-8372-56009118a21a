version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-translation
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=changeme
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=Asia/Taipei
      # 增加 Node.js 記憶體限制以處理大檔案
      - NODE_OPTIONS=--max-old-space-size=4096
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-translation-dependencies.json:/tmp/package.json
    command: >
      sh -c "
        echo '🔧 安裝翻譯工作流程依賴庫...' &&
        npm install -g mammoth xlsx &&
        echo '✅ 依賴庫安裝完成' &&
        echo '🚀 啟動 N8N...' &&
        n8n start
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可選：PostgreSQL 資料庫（用於生產環境）
  postgres:
    image: postgres:13
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_password
      - POSTGRES_DB=n8n
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # 可選：Redis（用於佇列管理）
  redis:
    image: redis:7-alpine
    container_name: n8n-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

volumes:
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: n8n-translation-network
