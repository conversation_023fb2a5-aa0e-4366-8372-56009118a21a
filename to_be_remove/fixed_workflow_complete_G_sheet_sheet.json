{"name": "My workflow 4 (Final Version)", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1220, -20], "id": "55022b48-cb67-43ce-b31d-0acd593de215", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-920, -20], "id": "1d34b768-fc56-4092-8628-386bf9fde97e", "name": "File Type Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-700, -80], "id": "cdffe0bc-95c9-44d1-af30-fd8640d2503f", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"sheetsToFormat": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-700, 60], "id": "910a2a2b-51fe-4e6f-9398-9145fbc7630e", "name": "Download Google Sheets as Excel", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 真實內容提取 - HTTP Export 方法\nconst item = $input.item;\nconst fileName = $('File Type Filter').item.json.name;\nconst fileId = $('File Type Filter').item.json.id;\n\n// 從 HTTP Export 響應中提取純文本內容\nconst extractedText = item.json.data.trim();\n\nif (!extractedText) {\n  throw new Error(`Failed to extract content from Google Doc: ${fileName}`);\n}\n\n// 清理和格式化文本\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, ' ')\n  .replace(/\\n/g, ' ')\n  .replace(/\\s+/g, ' ')\n  .trim();\n\nreturn {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-480, -80], "id": "43e1aa96-559e-474d-95f0-968088e0bb6e", "name": "Extract Docs Text"}, {"parameters": {"jsCode": "/**\n * 此節點接收一個二進位的 Excel 檔案，解析其內容，\n * 並將所有儲存格的文字合併成一個由換行符分隔的字串。\n */\n\n// 引入解析 Excel 的函式庫\nconst xlsx = require('xlsx');\n\n// 獲取上游節點的二進位檔案數據\nconst binaryData = $input.item.binary.data;\nif (!binaryData) {\n  throw new Error('No binary data received from the download node.');\n}\n\n// 將 Buffer 轉換為可供 xlsx 處理的格式\nconst workbook = xlsx.read(binaryData, { type: 'buffer' });\n\nlet combinedText = '';\nlet originalDataForRebuild = [];\n\n// 遍歷所有工作表\nworkbook.SheetNames.forEach(sheetName => {\n  const worksheet = workbook.Sheets[sheetName];\n  // 將工作表轉換為 JSON 格式\n  const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });\n  \n  // 為了重建，我們需要原始的行列結構\n  originalDataForRebuild.push(...jsonData);\n\n  // 遍歷每一行\n  jsonData.forEach(row => {\n    // 遍歷行中的每一個儲存格\n    row.forEach(cell => {\n      const value = cell ? String(cell) : '';\n      combinedText += value + '\\n';\n    });\n  });\n});\n\n// 獲取原始檔案資訊\nconst fileInfo = $('File Type Filter').item.json;\n\nreturn {\n  text: combinedText.trim(),\n  originalFileId: fileInfo.id,\n  originalFileName: fileInfo.name,\n  fileType: 'sheets',\n  // 儲存原始的、未經翻譯的行列數據，以便後續重建\n  originalData: originalDataForRebuild\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-480, 60], "id": "93660aa7-ebe2-45a6-a68d-fa09c5756531", "name": "Extract Sheets Text"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-80, -20], "id": "d798bfcf-d25a-4890-be00-11f4c01bf7e4", "name": "Merge Extracted Content"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a precise translation engine. The following input consists of text segments separated by newlines. Your task is to translate each segment into Traditional Chinese (Taiwan).\n**Crucially, you MUST preserve the exact number of newlines.** If a line is empty in the input, it must be empty in the output. Do not add any introductory text, explanations, or merge lines. The number of lines in your output must exactly match the number of lines in the input.\n\nInput Text:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [120, -20], "id": "def5b6e7-7fbe-4410-9dfe-4acb5cf83560", "name": "First Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 處理 Gemini API 回應\nconst geminiResponse = $json;\n\n// 嘗試從多個源獲取數據\nconst sourceData = $('Merge Extracted Content').item.json;\n\n// 解析翻譯結果\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\n// 傳遞完整數據到 Claude 進行改進\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, -20], "id": "fddc3967-3398-44d9-a8cd-80d69bbb2f03", "name": "Process Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, -20], "id": "870fa7ed-a32d-44f8-943b-89914306ce09", "name": "Reflective Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 解析 Claude API 回應\nconst claudeResponse = $json;\nconst upstreamData = $('Process Gemini Response').item.json;\n\nlet finalTranslation = upstreamData.initialTranslation;\n\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\n// 傳遞最終數據到輸出交換機\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, -20], "id": "6da134e6-c854-4d70-9592-d94486060753", "name": "Process Claude Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileType }}", "rightValue": "docs", "operator": {"type": "string", "operation": "equals"}, "id": "docs-condition"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "sheets", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [920, -20], "id": "f5f91369-8307-448a-b5ea-37084105a4cf", "name": "Output Type Switch"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容\nconst finalTranslation = $json.finalTranslation;\nconst originalFileName = $json.originalFileName.replace(/\\.(doc|docx|txt|pdf)$/i, '');\nconst finalTitle = `${originalFileName}_翻譯`;\n\nreturn {\n  title: finalTitle,\n  content: finalTranslation\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, -80], "id": "ca4a3976-7817-4fad-b6b1-29f79e36c87e", "name": "Prepare Docs Content"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, -80], "id": "e5415862-da7b-402c-8a39-5b9246994f33", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1440, -80], "id": "9f7866d8-3930-4504-ae50-fa298c9aec99", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "aKNvhMNpGwKLTvmj", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "/**\n * 此節點接收翻譯後的文本和原始的行列數據，\n * 將翻譯內容重新填入，並生成一個新的二進位 Excel 檔案。\n */\n\nconst xlsx = require('xlsx');\n\nconst translatedText = $json.finalTranslation;\nconst originalData = $json.originalData;\nconst originalFileName = $json.originalFileName.replace(/\\.(xls|xlsx|csv)$/i, '');\n\nif (!originalData) {\n  throw new Error('原始 Sheets 數據缺失，無法重建文件。');\n}\n\n// 將翻譯文本按行分割\nconst translatedLines = translatedText.split('\\n');\nlet lineIndex = 0;\n\nconst rebuiltData = [];\n\n// 遍歷原始的行列數據\nfor (const originalRow of originalData) {\n  const newRow = [];\n  for (const originalCell of originalRow) {\n    if (lineIndex < translatedLines.length) {\n      newRow.push(translatedLines[lineIndex]);\n    } else {\n      // 如果翻譯行數不足，則填入空字串\n      newRow.push('');\n    }\n    lineIndex++;\n  }\n  rebuiltData.push(newRow);\n}\n\n// 創建一個新的工作簿和工作表\nconst newWorkbook = xlsx.utils.book_new();\nconst newWorksheet = xlsx.utils.aoa_to_sheet(rebuiltData);\n\n// 將新工作表添加到工作簿\nxlsx.utils.book_append_sheet(newWorkbook, newWorksheet, 'Translated');\n\n// 將工作簿寫入 Buffer\nconst buffer = xlsx.write(newWorkbook, { bookType: 'xlsx', type: 'buffer' });\n\n// 輸出二進位檔案\nconst newFile = await $binary.create(buffer, `${originalFileName}_翻譯.xlsx`);\n\nreturn newFile;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 60], "id": "a514208e-82f8-4ffe-8678-ca0d2f9d8155", "name": "Rebuild & Create Excel File"}, {"parameters": {"name": "={{ $json.fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1560, 60], "id": "bac947a8-0663-4986-8b07-49c631d9fcf3", "name": "Upload Translated Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get the output from the \"Create Google Doc\" node (the current input)\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID from the \"Create Google Doc\" node.');\n}\n\n// Get the output from the \"Prepare Docs Content\" node\n// We use a node reference here because the content data was in the previous step\nconst prepNodeItems = $('Prepare Docs Content').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Prepare Docs Content\" node.');\n}\n\n// Find the content from the first item of that node's output\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Prepare Docs Content\" node.');\n}\n\n// Return the combined payload\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1240, -80], "id": "38abc06d-b15a-4f1a-8fe7-47d305181aa5", "name": "Prepare Final Payload"}], "connections": {"Google Drive Trigger": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Download Google Sheets as Excel", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Download Google Sheets as Excel": {"main": [[{"node": "Extract Sheets Text", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Extract Sheets Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 1}]]}, "Merge Extracted Content": {"main": [[{"node": "First Translation (<PERSON>)", "type": "main", "index": 0}]]}, "First Translation (Gemini)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>)", "type": "main", "index": 0}]]}, "Reflective Translation (Claude)": {"main": [[{"node": "Process Claude Response", "type": "main", "index": 0}]]}, "Process Claude Response": {"main": [[{"node": "Output Type Switch", "type": "main", "index": 0}]]}, "Output Type Switch": {"main": [[{"node": "Prepare Docs Content", "type": "main", "index": 0}], [{"node": "Rebuild & Create Excel File", "type": "main", "index": 0}]]}, "Prepare Docs Content": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}, "Rebuild & Create Excel File": {"main": [[{"node": "Upload Translated Sheet", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "id": "xTkG5CxPFcjTgxio", "tags": []}