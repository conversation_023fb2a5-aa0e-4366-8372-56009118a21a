{"name": "My workflow 4", "nodes": [{"parameters": {}, "name": "RAG Pipeline Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [-1060, 120], "id": "3f7e5b81-c494-4f3b-ad87-4be53fc3be49"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "151qVCX7FeAl90L1rIUd4CNyBj0puJI1s", "mode": "list", "cachedResultName": "RAG_Folder", "cachedResultUrl": "https://drive.google.com/drive/folders/151qVCX7FeAl90L1rIUd4CNyBj0puJI1s"}, "event": "fileUpdated", "options": {}}, "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-860, 120], "id": "8cbeb436-2b7c-4126-ba32-74a048dd1be0", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "value2": "application/pdf"}]}}, "name": "Check if PDF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-660, 40], "id": "698e4ade-588b-4058-a040-66fb18611629"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "operation": "contains", "value2": "presentation"}]}}, "name": "Check if PPT", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-660, 160], "id": "2e413d06-d079-4e21-806f-3a960a120995"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.mimeType}}", "operation": "contains", "value2": "word"}]}}, "name": "Check if DOC", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-660, 300], "id": "269987b9-1aeb-423b-b0e8-2d4513088334"}, {"parameters": {"operation": "download", "fileId": "={{$json.id}}", "options": {}}, "name": "Download PDF", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-460, 60], "id": "4bbeffc9-e948-43f5-b4e3-9227f4fc5be2", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "name": "Download PPT as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-460, 160], "id": "76e06de2-ce79-484e-9d81-e477e05d0417", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": "={{$json.id}}", "options": {"googleFileConversion": {"conversion": {"docsToFormat": "txt"}}}}, "name": "Download DOC as Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-460, 260], "id": "22ed414b-a939-44d6-8612-96a1667ff054", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 文件處理\nconst allItems = $input.all();\n\nfor (const item of allItems) {\n  const mimeType = item.json.mimeType || '';\n  \n  if (item.binary && item.binary.data) {\n    try {\n      if (mimeType === 'application/pdf') {\n        item.json.extractedText = 'PDF content extracted';\n        item.json.fileType = 'PDF';\n      } else if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {\n        const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n        item.json.extractedText = textContent;\n        item.json.fileType = 'PPT';\n      } else if (mimeType.includes('word')) {\n        const textContent = Buffer.from(item.binary.data.data).toString('utf8');\n        item.json.extractedText = textContent;\n        item.json.fileType = 'DOC';\n      }\n    } catch (error) {\n      item.json.extractedText = 'Error: ' + error.message;\n      item.json.fileType = 'Error';\n    }\n  }\n  \n  item.json.processedAt = new Date().toISOString();\n}\n\nreturn allItems;"}, "name": "Process All Files", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-260, 160], "id": "c90aab85-9deb-474c-9ce5-3d05f612a73b"}, {"parameters": {"resource": "embedding", "model": "text-embedding-3-small", "input": "={{$json.extractedText}}", "options": {"dimensions": 1536, "encodingFormat": "float"}, "requestOptions": {}}, "name": "Generate Document Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [-60, 160], "id": "96d0af50-8709-4118-b553-9df4c75e3852", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"resource": "row", "operation": "create", "tableId": "documents", "fieldsToSend": "defineBelow", "fields": {"values": [{"fieldId": "content", "fieldValue": "={{$json.extractedText}}"}, {"fieldId": "embedding", "fieldValue": "={{$json.data[0].embedding}}"}, {"fieldId": "file_name", "fieldValue": "={{$json.name}}"}, {"fieldId": "file_type", "fieldValue": "={{$json.fileType}}"}, {"fieldId": "created_at", "fieldValue": "={{$json.processedAt}}"}]}}, "name": "Insert to Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [160, 160], "id": "93345f24-6bb3-4e18-8625-f4cef4b93a1c", "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"path": "webhook-agent", "options": {}}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-860, 520], "id": "e1db4a1b-90cc-4071-8dc1-4ff487826172", "webhookId": "cfab1748-6d1f-4e50-9ffd-5a0c55f7341c"}, {"parameters": {"jsCode": "// 查詢分析\nconst query = $input.all()[0].json.body?.message || '';\nlet complexity = query.length < 50 ? 'simple' : 'complex';\n\nreturn [{\n  json: {\n    query: query,\n    complexity: complexity,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "name": "Smart Query Analyzer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-660, 520], "id": "1fce1191-7285-413f-90c6-3d38cacbce8a"}, {"parameters": {"resource": "embedding", "model": "text-embedding-3-small", "input": "={{$json.query}}", "options": {"dimensions": 1536, "encodingFormat": "float"}, "requestOptions": {}}, "name": "Generate Query Embedding", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [-460, 520], "id": "d052e03a-b0dc-4fcb-9814-a7eef09da3a3", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT content, file_name, file_type, (embedding <=> '{{$json.data[0].embedding}}') AS similarity FROM documents ORDER BY similarity LIMIT 5"}, "name": "Vector Search Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-260, 520], "id": "a3560336-8308-467f-bc23-51bc9ca7b4e8", "credentials": {"supabaseApi": {"id": "yPRBhBbqEC8Anxim", "name": "Supabase account"}}}, {"parameters": {"resource": "chat", "model": "gpt-4o-mini", "messages": {"values": [{"role": "system", "content": "You are a helpful assistant that answers questions based on provided context."}, {"role": "user", "content": "Based on the following context, answer the question:\n\nContext: {{$json.context}}\nQuestion: {{$json.query}}\n\nAnswer:"}]}, "options": {"maxTokens": 500, "temperature": 0.7}, "requestOptions": {}}, "name": "Generate Answer", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [-60, 520], "id": "6dbbd29f-5fcb-42d9-8a7b-b0b0d2da0e42", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"resource": "chat", "model": "gpt-4o-mini", "messages": {"values": [{"role": "system", "content": "You are a professional translator. Translate English text to Traditional Chinese accurately and naturally."}, {"role": "user", "content": "Please translate the following English text to Traditional Chinese:\n\n{{$json.choices[0].message.content}}"}]}, "options": {"maxTokens": 800, "temperature": 0.3}, "requestOptions": {}}, "name": "Translate to Chinese", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [160, 520], "id": "023e0671-3674-44a1-a047-9b45590a0db2", "credentials": {"openAiApi": {"id": "77k1ysYS944qlrxU", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"success\": true, \"answer\": $json.choices[0].message.content } }}", "options": {}}, "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [360, 520], "id": "25ac22a2-a593-40a0-9d93-0019dddb0104"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Check if PDF", "type": "main", "index": 0}, {"node": "Check if PPT", "type": "main", "index": 0}, {"node": "Check if DOC", "type": "main", "index": 0}]]}, "Check if PDF": {"main": [[{"node": "Download PDF", "type": "main", "index": 0}]]}, "Check if PPT": {"main": [[{"node": "Download PPT as Text", "type": "main", "index": 0}]]}, "Check if DOC": {"main": [[{"node": "Download DOC as Text", "type": "main", "index": 0}]]}, "Download PDF": {"main": [[{"node": "Process All Files", "type": "main", "index": 0}]]}, "Download PPT as Text": {"main": [[{"node": "Process All Files", "type": "main", "index": 0}]]}, "Download DOC as Text": {"main": [[{"node": "Process All Files", "type": "main", "index": 0}]]}, "Process All Files": {"main": [[{"node": "Generate Document Embedding", "type": "main", "index": 0}]]}, "Generate Document Embedding": {"main": [[{"node": "Insert to Supabase", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Smart Query Analyzer", "type": "main", "index": 0}]]}, "Smart Query Analyzer": {"main": [[{"node": "Generate Query Embedding", "type": "main", "index": 0}]]}, "Generate Query Embedding": {"main": [[{"node": "Vector Search Supabase", "type": "main", "index": 0}]]}, "Vector Search Supabase": {"main": [[{"node": "Generate Answer", "type": "main", "index": 0}]]}, "Generate Answer": {"main": [[{"node": "Translate to Chinese", "type": "main", "index": 0}]]}, "Translate to Chinese": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "b11fad4f-c36e-47f8-ad51-6697d4b3769b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}