# MS Word 文件翻譯工作流程修改說明

## 修改概述

根據您的要求，我已經修改了 N8N 翻譯工作流程，讓 MS Word 文件能夠：
1. **先轉換成 Google Doc** - 當偵測到 MS Word 檔案時，先將其轉換為 Google Doc 格式
2. **走原有的翻譯流程** - 使用現有的 Google Docs 翻譯管道（Gemini + Claude 雙重翻譯）
3. **最終轉換回 MS Word 格式** - 翻譯完成後，將結果轉換回 .docx 格式並上傳到指定資料夾

## 主要修改內容

### 1. Word 文件轉換節點
- **節點名稱**: `Convert Word to Google Doc`
- **功能**: 使用 Google Drive API 將 MS Word 文件轉換為 Google Doc
- **API 調用**: `POST /drive/v3/files/{fileId}/copy` 並設定 `mimeType: 'application/vnd.google-apps.document'`

### 2. 文本提取節點修改
- **原節點**: `Download Word File` + `Extract Word Text` (使用 mammoth.js)
- **新節點**: `Export Converted Google Doc as Text` + `Extract Word Text from Converted Doc`
- **改進**: 避免了 mammoth.js 依賴問題，使用 Google Docs 的文本導出功能

### 3. 翻譯流程整合
- Word 文件現在使用與 Google Docs 相同的翻譯管道：
  - `First Translation (Gemini) - Word`
  - `Reflective Translation (Claude) - Word`
  - 保持雙重翻譯的高品質

### 4. 輸出格式轉換
新增了三個節點來處理最終的 Word 格式輸出：

#### a. `Prepare Word Export`
- 準備導出參數和檔名
- 生成 Google Doc 到 Word 的導出 URL

#### b. `Export Google Doc as Word`
- 使用 Google Docs API 將翻譯後的文檔導出為 .docx 格式
- API 調用: `GET /document/d/{documentId}/export?format=docx`

#### c. `Upload Word File to Drive`
- 將導出的 Word 文件上傳到指定的輸出資料夾
- 使用 multipart upload 確保文件完整性

## 工作流程圖

```
MS Word 檔案
    ↓
File Type Filter (偵測 Word 檔案)
    ↓
Convert Word to Google Doc (轉換為 Google Doc)
    ↓
Export Converted Google Doc as Text (導出文本)
    ↓
Extract Word Text from Converted Doc (提取文本)
    ↓
First Translation (Gemini) - Word (第一次翻譯)
    ↓
Process Word Gemini Response (處理回應)
    ↓
Reflective Translation (Claude) - Word (精煉翻譯)
    ↓
Process Word Claude Response (處理回應)
    ↓
Word Filename Resolver (解析檔名)
    ↓
Get Word Filename API (獲取檔名)
    ↓
Process Word Filename Response (處理檔名)
    ↓
Create Google Doc for Word (創建翻譯文檔)
    ↓
Prepare Word Final Payload (準備內容)
    ↓
Add Content to Word Google Doc (添加翻譯內容)
    ↓
Prepare Word Export (準備導出)
    ↓
Export Google Doc as Word (導出為 Word)
    ↓
Upload Word File to Drive (上傳最終 Word 檔案)
```

## 技術優勢

### 1. 避免依賴問題
- 不再需要 mammoth.js 庫
- 使用 Google 原生 API 進行文件轉換

### 2. 保持翻譯品質
- 使用相同的雙重翻譯管道（Gemini + Claude）
- 保持與 Google Docs 翻譯相同的高品質

### 3. 格式保持
- 通過 Google Docs 作為中介，更好地保持文檔格式
- 最終輸出為標準 .docx 格式

### 4. 錯誤處理
- 每個步驟都有詳細的日誌記錄
- 包含錯誤處理和備案機制

## 檔案輸出

- **修改後的工作流程**: `modified_workflow_word_support.json`
- **原始檔案**: `fixed_workflow_complete_cleaned.json` (已修改)

## 使用說明

1. 將 `modified_workflow_word_support.json` 導入到您的 N8N 實例
2. 確認所有 Google Drive 和 Google Docs 憑證設定正確
3. 測試上傳一個 MS Word 文件到監控資料夾
4. 檢查輸出資料夾中的翻譯結果

## 注意事項

- 確保 Google Drive API 和 Google Docs API 都已啟用
- 檢查資料夾權限設定
- 第一次使用時建議先用小檔案測試
