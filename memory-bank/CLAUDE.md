# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an N8N workflow automation project that implements a **reflective translation system** for automatically translating Google Drive documents from English to Traditional Chinese. The workflow monitors a Google Drive folder, processes Google Docs and Sheets files, and performs high-quality translation using a dual-AI approach.

### Core Workflow Architecture

The main workflow (`My_workflow_Reflective_translation.json`) contains 16 nodes implementing:

1. **Trigger Layer**: Google Drive monitoring (every minute polling)
2. **Routing Layer**: File type detection (Google Docs vs Sheets)
3. **Extraction Layer**: Content extraction maintaining structure
4. **Translation Layer**: 3-stage translation process
   - Initial translation (Google Gemini Pro)
   - Dictionary correction (custom terminology)
   - Reflective improvement (Claude 3.5 Sonnet)
5. **Reconstruction Layer**: Format preservation and rebuilding
6. **Output Layer**: Automated file creation and organization

### Key Technical Patterns

- **Pipeline Filter Pattern**: Sequential processing through distinct stages
- **Strategy Pattern**: Different handling for Docs vs Sheets
- **Chain of Responsibility**: Multi-stage translation refinement
- **Observer Pattern**: Drive change event monitoring

## Development Commands

### N8N Workflow Management
```bash
# Import workflow
n8n import:workflow --file="My_workflow_Reflective_translation.json"

# Export workflow
n8n export:workflow --id="Zzy7eBh3vGV4RwzC" --output="My_workflow_Reflective_translation.json"

# Start N8N instance
n8n start

# View workflow executions
n8n executions:list --workflow="My workflow 3"
```

### Workflow Testing
```bash
# Test individual nodes
n8n execute --workflow="My workflow 3" --data='{"test": "data"}'

# Check workflow status
n8n workflow:list --active
```

## Critical Configuration

### Required API Credentials
- **Google Drive OAuth2**: `wXPA0LnYGj7CEvSq` (credential ID)
- **Google Gemini API**: For initial translation
- **Anthropic Claude API**: For reflective improvement
- **Google Docs OAuth2**: For document creation

### Key Folder IDs
- **Source Folder**: `1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X`
- **Output Folder**: `1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg`
- **Dictionary Sheet**: `1GY9wY5ibJibVHl1pGTSS6dSz1Kr2dj7fz0cmm9m_8gY`

## Memory Bank Structure

The `memory-bank/` directory contains comprehensive project documentation:

- **`projectbrief.md`**: Core objectives and functionality
- **`techContext.md`**: Technical stack and API configurations
- **`systemPatterns.md`**: Architecture patterns and design principles
- **`activeContext.md`**: Current development status and focus areas
- **`progress.md`**: Completion tracking and milestones
- **`productContext.md`**: Product requirements and user context

**Always read the memory bank files first** to understand the current project state and technical decisions.

## Translation Quality Framework

### 3-Stage Translation Process
1. **Initial Translation**: Gemini Pro with Taiwan-specific prompts
2. **Dictionary Correction**: Apply custom terminology mappings
3. **Reflective Improvement**: Claude 3.5 Sonnet for naturalness and accuracy

### Quality Dimensions
- Semantic accuracy (語意準確性)
- Language naturalness (語言自然性)
- Terminology consistency (用詞精準性)
- Context adaptation (語境適配性)
- Professional consistency (專業一致性)

## File Processing Patterns

### Google Docs Processing
```javascript
// Extract text content from binary data
text: Buffer.from($binary.data.data).toString('utf8')
```

### Google Sheets Processing
```javascript
// Combine all text content from sheets
Object.keys(sheet.json).forEach(key => {
  if (typeof value === 'string' && value.trim()) {
    combinedText += value + '\n';
  }
});
```

### Structure Preservation
- Maintain original file metadata (ID, name, type)
- Preserve sheet structure for reconstruction
- Add "_translated" suffix to output files

## Workflow Node Naming Conventions

- Use descriptive English names (e.g., "Extract Docs Text")
- Include action and target (e.g., "Download Google Sheets as Excel")
- Specify direction for translations (e.g., "First Translation (Gemini EN→ZH)")
- Use parentheses for clarification (e.g., "Reflective Translation (Claude)")

## Error Handling Patterns

- Each processing stage includes error recovery
- Preserve intermediate data for resumption
- Implement retry mechanisms for API calls
- Maintain execution state tracking

### Node Reference Safety
**Critical**: Use conditional length checks for cross-branch node references:
```javascript
// Safe pattern for referencing nodes from different execution branches
$('NodeName').length > 0 ? $('NodeName').first().json.field : fallbackValue
```

**Common Issue**: "Referenced node is unexecuted" errors occur when:
- Nodes reference data from conditional branches that didn't execute
- Using `.item.json` or `.first()?.json?.` may still fail in N8N
- Solution: Check `.length > 0` before accessing node data

## Deployment Considerations

- **Local Development**: Full control, requires continuous running
- **Cloud Hosting**: High availability, automatic scaling
- **Hybrid Approach**: Cloud N8N with local credential management

## Extension Points

- **New File Types**: Add branches in routing layer
- **Additional AI Engines**: Extend translation layer
- **Quality Checks**: Insert validation nodes
- **Output Formats**: Expand output processing options

## MCP (Model Context Protocol) Integration

### Installation and Setup
```bash
# Quick install
./install-mcp.sh

# Manual install
npm install n8n-nodes-mcp
```

### Environment Requirements
- N8N version 1.88.0 or higher
- Set `N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true`

### Docker Deployment
```bash
# Start with MCP support
docker-compose -f docker-compose.mcp.yml up -d
```

### MCP Configuration Files
- `docker-compose.mcp.yml`: Docker setup with MCP integration
- `.env.mcp`: Environment variables template
- `install-mcp.sh`: Automated installation script

### MCP Nodes Available
- **MCP Client Tool**: Connect to external MCP servers
- **MCP Server Trigger**: Receive requests from MCP clients
- **Community Package**: `n8n-nodes-mcp` for extended functionality

### Connection Methods
- **HTTP Streamable** (Recommended): Modern streaming protocol
- **Server-Sent Events** (Deprecated): Legacy compatibility

### Use Cases
- Connect AI agents (like Claude Desktop) to n8n workflows
- Enable LLMs to interact with external tools and data sources
- Create AI-driven automation workflows
- Bridge between AI models and enterprise systems