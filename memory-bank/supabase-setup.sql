-- Supabase Database Setup for English RAG with Chinese Translation Layer
-- URL: https://eezolsqijpwcdgmnrtgx.supabase.co
-- Run this in your Supabase SQL Editor

-- 1. Create documents table for storing extracted content
CREATE TABLE IF NOT EXISTS documents (
  id SERIAL PRIMARY KEY,
  content TEXT NOT NULL,
  filename VA<PERSON>HA<PERSON>(255),
  file_type VARCHAR(10),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Add indexes for better search performance
CREATE INDEX IF NOT EXISTS idx_documents_content ON documents USING gin(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_documents_filename ON documents(filename);
CREATE INDEX IF NOT EXISTS idx_documents_file_type ON documents(file_type);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);

-- 3. Enable Row Level Security (RLS)
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- 4. Create policy for anonymous access (for your N8N workflow)
CREATE POLICY "Allow anonymous access" ON documents
  FOR ALL 
  TO anon 
  USING (true)
  WITH CHECK (true);

-- 5. Create policy for authenticated users
CREATE POLICY "Allow authenticated access" ON documents
  FOR ALL 
  TO authenticated 
  USING (true)
  WITH CHECK (true);

-- 6. Optional: Create a function for better text search
CREATE OR REPLACE FUNCTION search_documents(search_query TEXT)
RETURNS TABLE(
  id INTEGER,
  content TEXT,
  filename VARCHAR(255),
  file_type VARCHAR(10),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE,
  similarity REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.content,
    d.filename,
    d.file_type,
    d.metadata,
    d.created_at,
    similarity(d.content, search_query) as sim
  FROM documents d
  WHERE d.content ILIKE '%' || search_query || '%'
  ORDER BY sim DESC, d.created_at DESC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- 7. Enable the pg_trgm extension for similarity search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 8. Create a more advanced search function using vector similarity (if you have embeddings)
-- Uncomment if you plan to use vector embeddings in the future
-- CREATE EXTENSION IF NOT EXISTS vector;
-- ALTER TABLE documents ADD COLUMN embedding vector(1536);
-- CREATE INDEX ON documents USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

COMMENT ON TABLE documents IS 'Stores extracted content from PDF, PPT, and DOC files for RAG system';
COMMENT ON COLUMN documents.content IS 'Extracted text content from the file';
COMMENT ON COLUMN documents.filename IS 'Original filename from Google Drive';
COMMENT ON COLUMN documents.file_type IS 'File type: PDF, PPT, or DOC';
COMMENT ON COLUMN documents.metadata IS 'Additional metadata from Google Drive and processing';