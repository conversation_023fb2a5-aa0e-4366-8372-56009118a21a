# N8N 翻譯工作流程 - 記憶庫

> 這是 Cline 的記憶庫，包含 N8N 反思式翻譯工作流程專案的完整背景資訊。每次新會話開始時，我都需要閱讀這些文件以重建專案理解。

## 📁 記憶庫結構

### 核心文件（必讀）
1. **[`projectbrief.md`](projectbrief.md)** - 專案基礎概述
   - 核心目標和主要功能
   - 技術架構概覽
   - 品質保證機制
   - 專案範圍定義

2. **[`productContext.md`](productContext.md)** - 產品背景分析
   - 問題定義和目標用戶
   - 解決方案價值主張
   - 使用者體驗目標
   - 預期成果和效益

3. **[`systemPatterns.md`](systemPatterns.md)** - 系統架構模式
   - 整體架構設計
   - 關鍵設計模式
   - 技術實現模式
   - 擴展點設計

4. **[`techContext.md`](techContext.md)** - 技術背景資訊
   - 核心技術棧
   - 開發環境需求
   - API 認證配置
   - 技術限制約束

5. **[`activeContext.md`](activeContext.md)** - 當前工作狀態
   - 專案當前進度
   - 技術重點和實現模式
   - 近期工作重點
   - 技術決策記錄

6. **[`progress.md`](progress.md)** - 進度追蹤
   - 整體完成度
   - 已完成功能清單
   - 待完成工作
   - 成功指標

## 🔄 使用流程

### 新會話開始時
1. **閱讀 [`projectbrief.md`](projectbrief.md)** - 快速理解專案目標
2. **檢視 [`progress.md`](progress.md)** - 了解當前進度狀態
3. **查閱 [`activeContext.md`](activeContext.md)** - 掌握最新工作重點
4. **參考其他文件** - 根據具體任務需要深入了解

### 文檔更新時機
- 🔄 **專案狀態變更**：更新 [`progress.md`](progress.md) 和 [`activeContext.md`](activeContext.md)
- 🏗️ **架構調整**：更新 [`systemPatterns.md`](systemPatterns.md)
- 🛠️ **技術變更**：更新 [`techContext.md`](techContext.md)
- 📋 **需求變化**：更新 [`productContext.md`](productContext.md)

## 📊 專案快速概覽

### 當前狀態
- **完成度**：95% - 完全分離架構已完成，準備最終測試
- **工作流程**：22 個節點的完全分離雙路徑 N8N 工作流程
- **翻譯方式**：Gemini + Claude 雙引擎反思式翻譯
- **支援格式**：Google Docs 和 Google Sheets
- **架構特色**：完全分離設計，避免 fileType 混亂問題

### 核心技術
- **工作流程引擎**：N8N
- **AI 翻譯**：Google Gemini Pro + Anthropic Claude 3.5 Sonnet
- **文件整合**：Google Drive API
- **語言對**：英文 → 繁體中文

### 關鍵特色
- **全自動化**：文件變更觸發 → 翻譯處理 → 結果輸出
- **品質保證**：三階段翻譯（初譯 → 字典校正 → 反思改進）
- **格式保持**：維持原始文件結構和佈局
- **術語一致**：外部字典確保專業術語統一

## 🎯 下次工作重點
1. **最終測試**：驗證完全分離架構的功能完整性
2. **API 狀態檢查**：確認 Gemini 和 Claude API 連接狀態
3. **生產部署**：啟動工作流並建立監控機制

## 📋 重要更新記錄
- **2025-06-16**: 完成架構重設計，實現完全分離的雙路徑系統
- **2025-06-13**: 完成核心功能開發和節點配置
- **2025-06-10**: 專案啟動和記憶庫建立

---
*記憶庫建立時間：2025年6月10日*
*最後更新：2025年6月16日*
*當前工作流程文件：[`fixed_workflow_complete_cleaned.json`](../fixed_workflow_complete_cleaned.json)*