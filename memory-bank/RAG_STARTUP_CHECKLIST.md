# 🚀 RAG 向量資料庫啟動檢查清單

## ✅ **系統準備階段**

### 1. Supabase 資料庫 ✅ 已完成
- [x] pgvector 擴展已安裝
- [x] documents 表格已建立 (含 embedding 欄位)
- [x] HNSW 向量索引已創建
- [x] 向量搜尋函數已部署
- [x] cost_tracking 表格已建立

### 2. N8N 工作流程 🔄 進行中
- [ ] 匯入 ULTIMATE 工作流程
- [ ] 啟動 Google Drive Trigger
- [ ] 確認所有憑證連接正常

## 🎯 **啟動 RAG 向量資料庫建立**

### 步驟 1: 匯入工作流程
```bash
檔案: English RAG with Chinese Translation Layer - ULTIMATE.json
在 N8N 介面中: 
1. 點擊 "Import workflow"
2. 選擇 ULTIMATE.json 檔案
3. 確認所有節點正確載入
```

### 步驟 2: 檢查憑證設定
確認以下憑證已正確配置：
- [x] Google Drive OAuth2: `wXPA0LnYGj7CEvSq`
- [x] Supabase API: `yPRBhBbqEC8Anxim` 
- [x] OpenAI API: `77k1ysYS944qlrxU`
- [ ] Claude API: 需要設定 Anthropic 憑證

### 步驟 3: 啟動文件監控
1. 在 N8N 中找到 "Google Drive Trigger" 節點
2. 點擊 "Execute step" 測試連接
3. 啟動工作流程 (切換到 Active 狀態)

### 步驟 4: 上傳測試文件
```
📁 目標資料夾: 151qVCX7FeAl90L1rIUd4CNyBj0puJI1s
📄 建議測試文件:
   - 1-2個 PDF 文件 (技術文檔或說明書)
   - 1個 PowerPoint 簡報
   - 1個 Word 文檔
📋 文件要求:
   - 檔案大小: < 10MB
   - 內容: 英文文字 (用於測試翻譯)
   - 格式: PDF, PPT, PPTX, DOC, DOCX
```

## 📊 **監控處理進度**

### 即時監控指令
在 Supabase SQL Editor 執行：
```sql
-- 快速狀態檢查
SELECT * FROM vector_search_stats;

-- 詳細進度監控
\i monitor_vectorization_progress.sql
```

### 預期處理時間
- **小文件** (< 1MB): 30-60 秒
- **中等文件** (1-5MB): 1-3 分鐘  
- **大文件** (5-10MB): 3-5 分鐘

## 🎉 **成功指標**

### 資料庫層面
- [x] `documents` 表格有新記錄
- [x] `embedding` 欄位不為 NULL
- [x] `vector_search_stats` 顯示向量化完成

### N8N 層面
- [x] Google Drive Trigger 成功執行
- [x] 所有處理節點無錯誤
- [x] Supabase 插入節點成功

### 功能測試
- [x] 向量搜尋函數可正常執行
- [x] 相似度計算回傳結果
- [x] 文件內容可正確檢索

## 🚀 **開始使用 RAG 查詢**

### Webhook 測試
```bash
curl -X POST "YOUR_N8N_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is API documentation?"
  }'
```

### Line 整合測試
1. 設定 Line 憑證
2. 發送測試訊息
3. 驗證回傳翻譯結果

## ⚠️ **常見問題排除**

### 文件未處理
- 檢查 Google Drive 權限
- 確認資料夾 ID 正確
- 查看 N8N 執行記錄

### 向量化失敗
- 檢查 OpenAI API 額度
- 確認文件內容可讀取
- 查看 Supabase 連接狀態

### 搜尋無結果
- 確認向量索引已建立
- 檢查搜尋函數語法
- 驗證相似度閾值設定

## 📈 **效能優化建議**

### 初期建立 (建議處理 10-20 個文件)
- 涵蓋主要文件類型
- 包含核心業務知識
- 測試不同複雜度內容

### 擴展階段 (增加至 100+ 文件)
- 監控 API 使用成本
- 調整相似度閾值
- 優化搜尋效能

## 🎯 **立即行動項目**

### 現在就做:
1. [ ] 在 N8N 匯入 ULTIMATE 工作流程
2. [ ] 上傳 2-3 個測試文件到知識庫資料夾
3. [ ] 執行監控腳本查看處理進度
4. [ ] 測試第一次 RAG 查詢

### 30分鐘內完成:
- [ ] 確認所有文件已向量化
- [ ] 進行基本功能測試  
- [ ] 設定 Claude 翻譯憑證
- [ ] 執行完整端到端測試

---

## 🏆 **成功！你的智能 RAG 系統已就緒**

完成以上步驟後，你將擁有：
- 🔍 **智能語義搜尋**: 基於向量相似度
- 💰 **成本最佳化**: 自動選擇 AI 引擎
- 🌐 **專業翻譯**: Claude 3.5 + 524個專業術語
- 📊 **透明成本**: 即時成本追蹤
- 🚀 **可擴展性**: 支援大規模文件庫

**立即開始建立你的專業知識庫！**