# 系統架構模式 - N8N 翻譯工作流程

## 整體架構
### 工作流程模式
```
觸發監控 → 文件分類 → 內容提取 → 翻譯處理 → 格式重建 → 輸出保存
```

### 核心組件架構
1. **監控層 (Trigger Layer)**
   - Google Drive Trigger：每分鐘輪詢指定資料夾
   - 事件類型：檔案更新 (fileUpdated)
   - 監控範圍：特定資料夾及其子資料夾

2. **分流層 (Routing Layer)**
   - File Type Filter：根據 MIME 類型分流
   - 支援類型：Google Docs、Google Sheets
   - 條件路由：確保不同文件類型走向對應處理路徑

3. **提取層 (Extraction Layer)**
   - Google Docs：轉換為純文字格式
   - Google Sheets：解析為結構化數據後提取文字
   - 元資料保存：檔案 ID、檔案名稱、檔案類型

4. **翻譯層 (Translation Layer)**
   - 雙引擎架構：Gemini Pro + Claude 3.5 Sonnet
   - 字典整合：Excel 格式的術語對照表
   - 反思式改進：初譯 → 字典校正 → AI 反思

5. **重建層 (Reconstruction Layer)**
   - 格式保持：根據原始結構重建翻譯內容
   - 資料對應：維持原始文件的欄位結構
   - 檔名處理：自動添加 "_translated" 後綴

6. **輸出層 (Output Layer)**
   - Google Docs：建立新的 Google 文件
   - Google Sheets：建立新的 Excel 檔案並上傳
   - 檔案管理：移動到指定輸出資料夾

## 關鍵設計模式

### 1. 管道過濾器模式 (Pipeline Filter)
```
文件輸入 → 類型識別 → 內容提取 → 翻譯處理 → 格式重建 → 結果輸出
```
- 每個階段獨立處理特定功能
- 資料在各階段間順序流動
- 易於擴展和維護

### 2. 策略模式 (Strategy Pattern)
```
文件類型檢測 → 選擇對應處理策略
├── Google Docs 策略：文字提取 + 文件重建
└── Google Sheets 策略：表格解析 + Excel 重建
```

### 3. 責任鏈模式 (Chain of Responsibility)
```
原始翻譯 → 字典校正 → AI 反思改進 → 最終輸出
```
- 每個環節專注特定品質改進
- 可動態調整處理鏈順序

### 4. 觀察者模式 (Observer Pattern)
```
Google Drive 變更事件 → 觸發器監聽 → 啟動翻譯流程
```

## 技術實現模式

### 錯誤處理策略
- **節點級錯誤處理**：每個節點具備錯誤捕獲機制
- **重試機制**：API 調用失敗時自動重試
- **失敗恢復**：保存中間狀態以支援斷點恢復

### 資料流設計
- **上下文保持**：關鍵資料在節點間傳遞
- **元資料追蹤**：原始檔案資訊全程保留
- **狀態管理**：每個處理階段的狀態可追溯

### 整合模式
- **OAuth2 認證**：統一的 Google 服務認證
- **API 標準化**：一致的 API 調用模式
- **配置外部化**：關鍵參數支援外部配置

## 擴展點設計
1. **新文件類型支援**：在分流層添加新的條件分支
2. **新翻譯引擎**：在翻譯層添加新的 API 調用節點
3. **品質檢查機制**：在翻譯層後增加額外檢查節點
4. **輸出格式支援**：在輸出層添加新的格式處理器