# N8N Credentials Setup Guide

## Supabase Configuration

### 1. Supabase Database Credentials
- **Name**: `Supabase RAG Database`
- **ID**: `supabase_rag_credentials`
- **URL**: `https://eezolsqijpwcdgmnrtgx.supabase.co`
- **API Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVlem9sc3FpanB3Y2RnbW5ydGd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5ODE1MzAsImV4cCI6MjA2NTU1NzUzMH0.sSzZaK10KY0bxTK4hFyv6bxzkmO1GgycYYXmWUcKyC4`

### 2. Database Setup Steps
1. 前往 Supabase Dashboard: https://supabase.com/dashboard
2. 選擇你的專案 (eezolsqijpwcdgmnrtgx)
3. 點擊左側選單 "SQL Editor"
4. 複製並執行 `supabase-setup.sql` 中的 SQL 指令
5. 確認 `documents` 表格已成功建立

### 3. 其他必要憑證

#### Google Drive OAuth2 ✅
- **Name**: `Google Drive credentials`
- **Client ID**: `************-eo8dtc4ov4slittsvbd3du4q10ouhvgd.apps.googleusercontent.com`
- **Client Secret**: `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`
- **狀態**: 完全配置
- **權限**: Drive API, Docs API, Sheets API

#### OpenAI API ✅
- **Name**: `OpenAI credentials`
- **ID**: `openai_rag_credentials`
- **API Key**: `********************************************************`
- **狀態**: 已配置
- **模型**: GPT-4-turbo

#### Line Bot API (可選)
- **Name**: `Line credentials`
- **ID**: `YOUR_LINE_CREDENTIALS_ID`
- **設定**: Channel Access Token

## Google Drive 資料夾設定

### 知識庫資料夾 ✅
- **用途**: 存放 PDF, PPT, DOC 檔案
- **URL**: https://drive.google.com/drive/u/2/folders/151qVCX7FeAl90L1rIUd4CNyBj0puJI1s
- **資料夾 ID**: `151qVCX7FeAl90L1rIUd4CNyBj0puJI1s`
- **狀態**: 已配置到工作流程

### 詞彙表 Google Sheets ✅
- **用途**: 存放中英對照詞彙
- **URL**: https://docs.google.com/spreadsheets/d/1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs/edit
- **文件 ID**: `1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs`
- **狀態**: 已配置到工作流程
- **格式**: A欄(English) | B欄(Traditional Chinese)

## N8N 憑證配置步驟

### 在 N8N 中設定 Supabase 憑證:
1. 開啟 N8N 管理介面
2. 前往 Settings → Credentials
3. 點擊 "Add Credential"
4. 選擇 "Supabase"
5. 填入以下資訊:
   - Name: `Supabase RAG Database`
   - Host: `eezolsqijpwcdgmnrtgx.supabase.co`
   - Database: `postgres` (預設)
   - Username: `postgres`
   - Password: 你的 Supabase database password
   - API Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVlem9sc3FpanB3Y2RnbW5ydGd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5ODE1MzAsImV4cCI6MjA2NTU1NzUzMH0.sSzZaK10KY0bxTK4hFyv6bxzkmO1GgycYYXmWUcKyC4`

### 測試連接
- 在任何 Supabase 節點中選擇剛建立的憑證
- 執行測試查詢確認連接正常

## 注意事項
- API Keys 為敏感資訊，請勿分享
- 定期檢查 Supabase 使用量避免超出限額
- 建議定期備份資料庫
- 可考慮設定 RLS (Row Level Security) 策略增強安全性