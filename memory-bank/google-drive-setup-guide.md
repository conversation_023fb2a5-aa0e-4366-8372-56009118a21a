# Google Drive API 設定指南

## 已配置的憑證資訊

**Client ID**: `************-eo8dtc4ov4slittsvbd3du4q10ouhvgd.apps.googleusercontent.com`

## 完整設定步驟

### 1. Google Cloud Console 設定

#### 啟用必要的 API
前往 [Google Cloud Console](https://console.cloud.google.com/) 並啟用以下 API：
- ✅ Google Drive API
- ✅ Google Docs API  
- ✅ Google Sheets API

#### OAuth 2.0 憑證設定
1. 前往 **API 和服務** → **憑證**
2. 點擊 **建立憑證** → **OAuth 2.0 用戶端 ID**
3. 應用程式類型選擇：**桌面應用程式**
4. 名稱：`N8N RAG System`
5. 已建立的 Client ID：`************-eo8dtc4ov4slittsvbd3du4q10ouhvgd.apps.googleusercontent.com`

### 2. N8N 憑證配置

#### 在 N8N 中設定 Google Drive OAuth2：
1. 前往 N8N → **Settings** → **Credentials**
2. 點擊 **Add Credential**
3. 選擇 **Google Drive OAuth2 API**
4. 填入以下資訊：
   ```
   Name: Google Drive credentials
   Client ID: ************-eo8dtc4ov4slittsvbd3du4q10ouhvgd.apps.googleusercontent.com
   Client Secret: [從 Google Cloud Console 取得]
   ```
5. 點擊 **Connect my account** 完成 OAuth 授權

### 3. Google Drive 資料夾結構設定

#### 建議的資料夾結構：
```
📁 RAG_Knowledge_Base/
├── 📄 document1.pdf
├── 📄 presentation1.pptx
├── 📄 report1.docx
└── 📄 更多文件...

📊 RAG_Glossary (Google Sheets)
├── Column A: English
├── Column B: Traditional Chinese
```

#### 詞彙表格式範例：
| English | Traditional Chinese |
|---------|-------------------|
| workflow | 工作流程 |
| RAG | 檢索增強生成 |
| machine learning | 機器學習 |
| artificial intelligence | 人工智慧 |
| database | 資料庫 |
| API | 應用程式介面 |

### 4. 取得必要的 ID

#### 獲取資料夾 ID：
1. 在 Google Drive 中開啟目標資料夾
2. 從 URL 複製資料夾 ID
   ```
   範例 URL: https://drive.google.com/drive/folders/1ABCD1234567890XYZ
   資料夾 ID: 1ABCD1234567890XYZ
   ```

#### 獲取 Google Sheets ID：
1. 開啟詞彙表 Google Sheets
2. 從 URL 複製文件 ID
   ```
   範例 URL: https://docs.google.com/spreadsheets/d/1XYZ9876543210ABCD/edit
   文件 ID: 1XYZ9876543210ABCD
   ```

### 5. 更新 N8N 工作流程配置

在工作流程 JSON 中更新以下參數：

```json
{
  "folderId": "YOUR_KNOWLEDGE_BASE_FOLDER_ID",    // 替換為實際資料夾 ID
  "fileId": "YOUR_GLOSSARY_SHEET_ID"             // 替換為實際詞彙表 ID
}
```

### 6. 權限設定

#### Google Drive 權限：
- **檔案讀取權限**：讀取 PDF/PPT/DOC 檔案
- **資料夾監控權限**：監控檔案變更
- **Sheets 讀取權限**：讀取詞彙表

#### 確保權限設定：
1. N8N 服務帳號有資料夾的讀取權限
2. 詞彙表 Google Sheets 設為"任何人都可檢視"或分享給服務帳號

### 7. 測試連線

執行測試腳本確認設定：
```bash
python test_google_drive.py
```

### 8. 常見問題解決

#### 問題：OAuth 授權失敗
**解決方案**：
- 檢查 Client ID 和 Client Secret 是否正確
- 確認回調 URL 設定正確
- 檢查 API 是否已啟用

#### 問題：無法訪問檔案
**解決方案**：
- 檢查檔案權限設定
- 確認 N8N 服務帳號有適當權限
- 驗證檔案 ID 是否正確

#### 問題：API 配額超限
**解決方案**：
- 檢查 Google Cloud Console 中的 API 使用量
- 考慮升級 API 配額
- 優化查詢頻率

### 9. 安全建議

- 🔐 不要在程式碼中硬編碼憑證
- 🔄 定期輪換 API 金鑰
- 📊 監控 API 使用量
- 🚫 限制 OAuth 權限範圍
- 📝 記錄 API 調用以便除錯

### 10. 下一步

設定完成後：
1. ✅ 上傳測試檔案到知識庫資料夾
2. ✅ 建立並填入詞彙表
3. ✅ 在 N8N 中匯入工作流程
4. ✅ 測試端到端功能
5. ✅ 開始使用 RAG 系統！