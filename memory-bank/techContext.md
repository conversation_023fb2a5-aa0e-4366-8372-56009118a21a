# 技術背景 - N8N 翻譯工作流程

## 核心技術棧

### 工作流程引擎
- **N8N**：開源工作流程自動化平台
  - 版本：最新穩定版
  - 部署模式：本地部署或雲端託管
  - 執行順序：v1 (順序執行模式)

### AI 翻譯引擎
1. **Google Gemini Pro**
   - API：Generative Language API v1beta
   - 模型：gemini-pro
   - 用途：初始英文到中文翻譯
   - 提示工程：針對台灣讀者優化的翻譯指令

2. **Anthropic Claude 3.5 Sonnet**
   - API 版本：2023-06-01
   - 模型：claude-3-5-sonnet-20240620
   - 用途：反思式翻譯改進
   - 最大 Token：4096
   - 特化角色：資深翻譯審校專家

### 整合服務
1. **Google Drive API**
   - 認證：OAuth2
   - 功能：文件監控、下載、上傳、移動
   - 支援格式：Google Docs、Google Sheets

2. **Google Docs API**
   - 認證：OAuth2
   - 功能：建立新文件、內容寫入

3. **Spreadsheet 處理**
   - 格式：XLSX (Excel)
   - 處理：讀取、解析、重建、建立

## 開發環境需求

### 系統需求
- **作業系統**：跨平台支援 (Windows/macOS/Linux)
- **Node.js**：建議 18+ 版本
- **記憶體**：建議 4GB+ RAM
- **儲存空間**：基礎安裝需要 1GB+

### API 認證需求
1. **Google 服務認證**
   - Google Drive OAuth2 認證
   - Google Docs OAuth2 認證
   - Google PaLM/Gemini API 金鑰

2. **Anthropic 認證**
   - Anthropic API 金鑰
   - Claude 模型訪問權限

### 網路需求
- **穩定網路連線**：用於 API 調用
- **HTTPS 支援**：所有 API 調用需要 HTTPS
- **防火牆設定**：確保 API 端點可達

## 技術限制與約束

### API 限制
1. **Google Gemini**
   - 速率限制：需遵守 API 配額
   - 文本長度：單次請求有字元限制
   - 併發限制：同時請求數量限制

2. **Anthropic Claude**
   - Token 限制：單次對話最大 4096 tokens
   - 速率限制：每分鐘請求數限制
   - 成本考量：按 token 使用量計費

3. **Google Drive**
   - 檔案大小：單檔下載上傳限制
   - API 配額：每日請求次數限制
   - 併發操作：同時操作文件數限制

### 技術約束
- **文件格式**：僅支援 Google Docs 和 Sheets
- **語言對**：目前僅支援英文到繁體中文
- **文字內容**：主要處理文字，不支援圖片翻譯
- **即時性**：受限於輪詢頻率 (每分鐘檢查)

## 配置管理

### 環境變數
```bash
# Google 服務認證
GOOGLE_DRIVE_OAUTH2_ID="wXPA0LnYGj7CEvSq"
GOOGLE_GEMINI_API_KEY="your-gemini-api-key"

# Anthropic 認證
ANTHROPIC_API_KEY="your-anthropic-api-key"

# 工作流程配置
SOURCE_FOLDER_ID="1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X"
OUTPUT_FOLDER_ID="1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg"
DICTIONARY_SHEET_URL="1GY9wY5ibJibVHl1pGTSS6dSz1Kr2dj7fz0cmm9m_8gY"
```

### 關鍵配置
- **輪詢頻率**：每分鐘檢查一次
- **監控範圍**：特定 Google Drive 資料夾
- **輸出位置**：指定的翻譯結果資料夾
- **字典來源**：Google Sheets 格式的術語表

## 部署考量

### 本地部署
- **優點**：完全控制、無外部依賴
- **缺點**：需要持續運行、維護成本高

### 雲端部署
- **優點**：高可用性、自動擴展
- **缺點**：成本較高、依賴雲端服務

### 混合部署
- **N8N 引擎**：雲端託管
- **API 金鑰**：本地安全管理
- **文件儲存**：Google Drive 雲端