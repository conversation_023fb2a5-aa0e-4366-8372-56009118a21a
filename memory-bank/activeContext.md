# 當前工作背景 - N8N 翻譯工作流程

## 專案當前狀態
### 完成的工作
1. **工作流程重大重設計**：完全分離的雙路徑架構
2. **核心功能實現**：所有主要翻譯功能節點已配置並優化
3. **API 整合完成**：Google 服務和 AI 翻譯引擎已整合
4. **記憶庫建立**：已建立完整的專案文檔結構
5. **架構問題解決**：移除了 fileType 混亂的根本原因

### 工作流程節點配置
**已配置的 22 個節點（完全分離架構）：**

**觸發與分流 (2 個節點):**
1. `Google Drive Trigger` - 監控文件變更
2. `File Type Filter` - 文件類型分流

**Docs 處理路徑 (10 個節點):**
3. `Export Google Docs as Text` - Docs 文件匯出
4. `Extract Docs Text` - 提取 Docs 文字內容
5. `First Translation (Gemini) - Docs` - Docs 專用 Gemini 翻譯
6. `Process Docs Gemini Response` - 處理 Docs Gemini 回應
7. `Reflective Translation (Claude) - Docs` - Docs 專用 Claude 改進
8. `Process Docs Claude Response` - 處理 Docs Claude 回應
9. `Docs Filename Resolver` - Docs 檔名解析
10. `Get Docs Filename API` - 獲取 Docs 檔名
11. `Process Docs Filename Response` - 處理檔名回應
12. `Create Google Doc via Drive API` - 創建新 Google Doc
13. `Prepare Final Payload` - 準備最終內容
14. `Add Content to Google Doc` - 添加內容到文件

**Sheets 處理路徑 (10 個節點):**
15. `Read Sheet Data` - 讀取 Sheets 數據
16. `Format Sheet Data` - 格式化 Sheets 數據
17. `First Translation (Gemini) - Sheets` - Sheets 專用 Gemini 翻譯
18. `Process Sheets Gemini Response` - 處理 Sheets Gemini 回應
19. `Reflective Translation (Claude) - Sheets` - Sheets 專用 Claude 改進
20. `Process Sheets Claude Response` - 處理 Sheets Claude 回應
21. `Rebuild Translated Sheet Data` - 重建翻譯後的表格數據
22. `Filename Resolver` - Sheets 檔名解析
23. `Get Filename API` - 獲取檔名 API
24. `Process Filename Response` - 處理檔名回應
25. `Create Translated Sheet` - 創建翻譯後的表格
26. `Prepare Sheet Write` - 準備表格寫入
27. `Move to Output Folder` - 移動到輸出資料夾
28. `Rebuild Write Payload` - 重建寫入負載
29. `Write to Sheet` - 寫入表格

## 當前技術重點

### 反思式翻譯方法論
**三階段翻譯流程：**
1. **初始翻譯** (Gemini Pro)
   - 英文到繁體中文的基礎翻譯
   - 針對台灣讀者優化的提示詞
   - 保持原文語意和語調

2. **字典校正** (JavaScript)
   - 載入預定義的術語對照表
   - 自動替換專業術語翻譯
   - 確保術語翻譯的一致性

3. **反思改進** (Claude 3.5 Sonnet)
   - 語意準確性檢查
   - 語言自然性改善
   - 用詞精準性優化
   - 語境適配性調整

### 關鍵實現模式
1. **完全分離架構**：Docs 和 Sheets 從 File Type Filter 後完全獨立
2. **資料保持策略**：原始文件結構信息全程保留
3. **早期分流**：避免後期 fileType 混亂問題
4. **錯誤恢復**：每個路徑都有獨立的錯誤處理機制
5. **格式重建**：翻譯後重建原始文件格式
6. **API 備案機制**：翻譯 API 失敗時的降級處理

## 當前配置重點

### API 整合配置
- **Google Drive 監控**：每分鐘輪詢特定資料夾
- **認證管理**：OAuth2 統一認證機制
- **字典來源**：Google Sheets 格式的翻譯對照表
- **輸出管理**：自動檔名處理和資料夾歸檔

### 品質控制機制
- **雙重引擎驗證**：Gemini + Claude 組合確保品質
- **術語一致性**：外部字典確保專業術語統一
- **格式完整性**：保持原始文件的結構和佈局
- **可追溯性**：完整的處理鏈路可回溯

## 近期工作重點

### 部署準備
1. **認證配置**：設置所有必要的 API 認證
2. **測試驗證**：完整流程的端到端測試
3. **性能調優**：優化 API 調用頻率和錯誤處理
4. **監控設置**：建立工作流程運行監控

### 功能擴展考量
1. **支援更多文件格式**：PDF、Word 等格式支援
2. **多語言支援**：擴展到其他語言對
3. **批量處理優化**：提升大量文件的處理效率
4. **品質評估機制**：加入翻譯品質自動評估

## 技術決策記錄

### 已確定的技術選擇
1. **N8N 作為工作流程引擎**：低代碼、視覺化、易維護
2. **完全分離的雙路徑架構**：避免交叉污染和 fileType 混亂
3. **雙 AI 引擎架構**：Gemini 初譯 + Claude 改進
4. **Google Drive 整合**：原生支援、無縫整合
5. **反思式翻譯方法**：多層次品質保證機制
6. **早期分流策略**：在數據提取後立即分離處理路徑

### 待決定的技術議題
1. **部署策略**：本地 vs 雲端部署選擇
2. **擴容方案**：高負載情況下的處理策略
3. **備份機制**：工作流程配置和資料備份方案
4. **版本控制**：工作流程變更的版本管理