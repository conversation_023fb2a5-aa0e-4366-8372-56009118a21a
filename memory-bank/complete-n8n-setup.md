# N8N 完整設定指南 🚀

## 系統狀態總覽

**✅ 所有 API 連線測試通過**
- Supabase 資料庫: 5/5 測試通過
- Google Drive API: 4/4 測試通過  
- OpenAI API: 6/6 測試通過

## 在 N8N 中建立憑證

### 1. Supabase 憑證設定

1. 前往 N8N → **Settings** → **Credentials**
2. 點擊 **Add Credential**
3. 選擇 **Supabase**
4. 填入資訊：
   ```
   Name: Supabase RAG Database
   Host: eezolsqijpwcdgmnrtgx.supabase.co
   Database: postgres
   Username: postgres
   Password: [你的 Supabase 資料庫密碼]
   API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.sSzZaK10KY0bxTK4hFyv6bxzkmO1GgycYYXmWUcKyC4
   ```

### 2. Google Drive OAuth2 憑證設定

1. 前往 N8N → **Settings** → **Credentials**  
2. 點擊 **Add Credential**
3. 選擇 **Google Drive OAuth2 API**
4. 填入資訊：
   ```
   Name: Google Drive credentials
   Client ID: ************-eo8dtc4ov4slittsvbd3du4q10ouhvgd.apps.googleusercontent.com
   Client Secret: 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
   ```
5. 點擊 **Connect my account** 完成 OAuth 授權

### 3. OpenAI API 憑證設定

1. 前往 N8N → **Settings** → **Credentials**
2. 點擊 **Add Credential**  
3. 選擇 **OpenAI API**
4. 填入資訊：
   ```
   Name: OpenAI credentials
   API Key: ********************************************************
   Organization: (可選填)
   ```

## 建立 Google Drive 資源

### 1. 建立知識庫資料夾

1. 前往 [Google Drive](https://drive.google.com)
2. 點擊 **新增** → **資料夾**
3. 命名為：`RAG_Knowledge_Base`
4. 上傳測試檔案（PDF, PPT, DOC）
5. 複製資料夾 ID：
   - 開啟資料夾
   - 從 URL 複製：`https://drive.google.com/drive/folders/[資料夾ID]`

### 2. 建立詞彙表 Google Sheets

1. 前往 [Google Sheets](https://sheets.google.com)
2. 點擊 **空白** 建立新試算表
3. 命名為：`RAG_Glossary`
4. 設定格式：
   ```
   A1: English          B1: Traditional Chinese
   A2: workflow         B2: 工作流程
   A3: RAG              B3: 檢索增強生成
   A4: machine learning B4: 機器學習
   A5: AI               B5: 人工智慧
   ```
5. 複製文件 ID：
   - 從 URL 複製：`https://docs.google.com/spreadsheets/d/[文件ID]/edit`

### 3. 設定權限

確保資料夾和詞彙表的分享設定為：
- **知道連結的使用者皆可檢視**
- 或直接分享給你的 Google 帳號

## 更新工作流程 ID

在工作流程 JSON 中更新以下位置：

### ✅ 資料夾 ID 已更新：
```json
"folderId": "151qVCX7FeAl90L1rIUd4CNyBj0puJI1s"
```
**工作流程已自動配置此資料夾 ID**

### ✅ 詞彙表 ID 已更新：
```json
"fileId": "1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs"
```
**工作流程已自動配置此詞彙表 ID**

### ⚠️ 重要：設定詞彙表分享權限
1. 開啟詞彙表：https://docs.google.com/spreadsheets/d/1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs/edit
2. 點擊右上角「共用」按鈕
3. 設定為「知道連結的使用者皆可檢視」
4. 或直接分享給你的 Google 帳號

## 匯入工作流程到 N8N

### 1. 匯入 JSON 檔案

1. 前往 N8N 主介面
2. 點擊 **Import from file**
3. 選擇：`English RAG with Chinese Translation Layer.json`
4. 點擊 **Import**

### 2. 檢查節點配置

確認以下節點的憑證設定：
- ✅ **Google Drive Trigger** → Google Drive credentials
- ✅ **Insert to Superbase** → Supabase RAG Database  
- ✅ **Search Superbase** → Supabase RAG Database
- ✅ **OpenAI (Generate English Answer)** → OpenAI credentials
- ✅ **Translate to Chinese** → OpenAI credentials

### 3. 測試各節點

#### 測試 Google Drive 連線：
1. 點擊 **Google Drive Trigger** 節點
2. 點擊 **Test step**
3. 確認可以讀取到你的資料夾

#### 測試 Supabase 連線：
1. 點擊 **Search Superbase** 節點  
2. 點擊 **Test step**
3. 確認可以查詢資料庫

#### 測試 OpenAI 連線：
1. 點擊 **OpenAI (Generate English Answer)** 節點
2. 點擊 **Test step**  
3. 確認 GPT-4 回應正常

## 端到端測試

### 1. 測試文件處理流程

1. 上傳一個測試 PDF 到你的知識庫資料夾
2. 在 N8N 中執行工作流程
3. 檢查 Supabase 中是否有新記錄
4. 驗證文字提取是否正確

### 2. 測試查詢和翻譯

1. 使用 Webhook 或 Line Bot 發送查詢
2. 確認系統能找到相關文件
3. 驗證英文回答生成
4. 檢查中文翻譯品質
5. 確認詞彙表術語使用正確

## 常見問題排除

### 問題：OAuth 授權失敗
**解決方案**：
- 檢查 Client ID 和 Secret 是否正確
- 確認 Google Cloud Console 中 API 已啟用
- 檢查回調 URL 設定

### 問題：Supabase 連線失敗  
**解決方案**：
- 檢查 API Key 是否正確
- 確認資料庫密碼
- 驗證表格是否存在

### 問題：OpenAI API 錯誤
**解決方案**：
- 檢查 API Key 餘額
- 確認模型權限
- 檢查速率限制

### 問題：文件無法讀取
**解決方案**：  
- 檢查 Google Drive 權限設定
- 確認資料夾 ID 正確
- 驗證檔案格式支援

## 系統監控建議

### 1. 定期檢查
- ✅ Supabase 使用量
- ✅ OpenAI API 使用量  
- ✅ Google Drive API 配額
- ✅ 工作流程執行記錄

### 2. 效能優化
- 考慮使用向量嵌入改善搜尋
- 實施快取機制
- 最佳化查詢頻率

### 3. 安全維護
- 定期輪換 API Keys
- 檢查存取權限
- 備份重要資料

---

**🎉 設定完成後，你的 RAG 系統將能夠：**
- 📁 自動處理 PDF/PPT/DOC 檔案
- 🔍 智能搜尋相關內容  
- 🤖 生成精確的英文回答
- 🌐 提供高品質中文翻譯
- 📚 使用自定義詞彙表確保術語一致性