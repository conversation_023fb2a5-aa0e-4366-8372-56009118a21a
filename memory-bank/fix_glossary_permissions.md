# 詞彙表權限設定指南

## 🔒 目前狀況

詞彙表 Google Sheets 目前**無法公開存取**，需要設定分享權限才能讓 N8N 工作流程讀取。

**詞彙表連結**: https://docs.google.com/spreadsheets/d/1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs/edit

## 📋 解決步驟

### 方法一：設定公開檢視權限（推薦）

1. **開啟詞彙表**：
   - 點擊連結：https://docs.google.com/spreadsheets/d/1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs/edit

2. **設定分享權限**：
   - 點擊右上角「**共用**」按鈕
   - 在「一般存取權」區域
   - 選擇「**知道連結的使用者皆可檢視**」
   - 點擊「**完成**」

3. **驗證設定**：
   - 嘗試在無痕模式開啟連結
   - 確認可以檢視內容

### 方法二：直接分享給特定帳號

1. **開啟詞彙表**
2. **點擊「共用」**
3. **輸入你的 Google 帳號 email**
4. **設定權限為「檢視者」**
5. **點擊「傳送」**

## 📊 建議的詞彙表格式

確保你的詞彙表包含以下格式：

```
    A欄              B欄
1   English          Traditional Chinese
2   workflow         工作流程
3   RAG              檢索增強生成
4   machine learning 機器學習
5   AI               人工智慧
6   database         資料庫
7   API              應用程式介面
8   node             節點
9   trigger          觸發器
10  credentials      憑證
```

## 🔧 技術說明

### N8N 如何存取詞彙表：

1. **"Load Glossary Sheet"** 節點使用 Google Drive API
2. 下載 Google Sheets 為 CSV 格式
3. **"Parse Glossary"** 節點解析 CSV 內容
4. 轉換為 JSON 格式供翻譯節點使用

### 權限要求：

- Google Sheets 必須可被匿名存取（公開檢視）
- 或分享給 N8N 使用的 Google 帳號
- CSV 匯出功能必須可用

## ✅ 設定完成後測試

設定權限後，可以測試以下 URL 是否可存取：

```
https://docs.google.com/spreadsheets/d/1Xdvz-HdjRYisV2Gd6BMR8FLHy5Gisqb0o-KC9e_NJbs/export?format=csv
```

如果可以下載 CSV 檔案，表示權限設定正確。

## 🚨 常見問題

### 問題：設定後仍無法存取
**解決方案**：
- 確認選擇「知道連結的使用者皆可檢視」而非「受限制」
- 等待 1-2 分鐘讓設定生效
- 嘗試重新整理頁面

### 問題：CSV 匯出失敗
**解決方案**：
- 檢查工作表是否有多個分頁（gid 參數）
- 確認文件 ID 正確
- 驗證網路連線

### 問題：詞彙表格式錯誤
**解決方案**：
- 確保第一行為標題（English, Traditional Chinese）
- 每行包含兩欄內容
- 避免空白行或格式異常

---

**📞 需要協助？**

設定完成後，請執行以下測試指令驗證：
```bash
python test_glossary_sheet.py
```

或直接告訴我「詞彙表權限已設定」，我會重新進行測試確認。