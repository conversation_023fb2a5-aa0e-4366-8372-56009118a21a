# 專案簡介 - N8N 反思式翻譯工作流程

## 核心目標
建立一個智能化的文件翻譯自動化系統，監控 Google Drive 文件變更並執行高品質的英中翻譯流程。

## 主要功能
1. **自動監控**：監控指定 Google Drive 資料夾的文件變更
2. **文件類型支援**：支援 Google Docs 和 Google Sheets 文件
3. **雙重翻譯引擎**：結合 Gemini 和 Claude 的反思式翻譯方法
4. **術語字典整合**：使用預定義字典確保專業術語翻譯的一致性
5. **結構保持**：維持原始文件的格式和結構
6. **自動輸出**：將翻譯結果保存到指定的 Google Drive 資料夾

## 技術架構
- **平台**：N8N 工作流程自動化
- **觸發機制**：Google Drive Trigger (每分鐘檢查)
- **翻譯引擎**：Google Gemini Pro + Anthropic Claude 3.5 Sonnet
- **支援格式**：Google Docs (.docx) 和 Google Sheets (.xlsx)
- **字典來源**：Google Sheets 格式的翻譯字典

## 品質保證
- **反思式翻譯**：初步翻譯 → 字典校正 → AI 反思改進
- **語言自然性**：專注於台灣讀者的語言習慣
- **專業術語一致性**：透過字典確保術語翻譯的統一

## 專案範圍
- 監控單一 Google Drive 資料夾
- 英文到繁體中文的翻譯
- 支援文字內容的自動化處理
- 保持原始文件的結構和格式