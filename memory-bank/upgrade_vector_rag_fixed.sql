-- 修正版 Supabase 向量 RAG 升級
-- 使用 OpenAI text-embedding-3-small (維度: 1536，符合 Supabase 限制)

-- 1. 啟用 pgvector 擴展
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 修改 documents 表格添加向量欄位 (1536維度)
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS embedding vector(1536);

-- 3. 創建向量索引 (提升搜尋效能) - 使用 HNSW 索引避免維度限制
CREATE INDEX IF NOT EXISTS idx_documents_embedding_hnsw
ON documents USING hnsw (embedding vector_cosine_ops);

-- 4. 創建向量相似性搜尋函數
CREATE OR REPLACE FUNCTION search_documents_by_vector(
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.5,
  match_limit int DEFAULT 5
)
RETURNS TABLE(
  id integer,
  content text,
  filename varchar(255),
  file_type varchar(10),
  metadata jsonb,
  created_at timestamp with time zone,
  similarity float
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.content,
    d.filename,
    d.file_type,
    d.metadata,
    d.created_at,
    1 - (d.embedding <=> query_embedding) as similarity_score
  FROM documents d
  WHERE d.embedding IS NOT NULL
    AND 1 - (d.embedding <=> query_embedding) > similarity_threshold
  ORDER BY d.embedding <=> query_embedding
  LIMIT match_limit;
END;
$$ LANGUAGE plpgsql;

-- 5. 創建混合搜尋函數 (關鍵字 + 向量)
CREATE OR REPLACE FUNCTION hybrid_search_documents(
  search_query text,
  query_embedding vector(1536),
  similarity_threshold float DEFAULT 0.3,
  match_limit int DEFAULT 10
)
RETURNS TABLE(
  id integer,
  content text,
  filename varchar(255),
  file_type varchar(10),
  metadata jsonb,
  created_at timestamp with time zone,
  similarity float,
  search_type text
) AS $$
BEGIN
  -- 向量搜尋結果
  RETURN QUERY
  SELECT 
    d.id,
    d.content,
    d.filename,
    d.file_type,
    d.metadata,
    d.created_at,
    1 - (d.embedding <=> query_embedding) as similarity_score,
    'vector'::text as search_type
  FROM documents d
  WHERE d.embedding IS NOT NULL
    AND 1 - (d.embedding <=> query_embedding) > similarity_threshold
  ORDER BY d.embedding <=> query_embedding
  LIMIT match_limit / 2;

  -- 關鍵字搜尋結果
  RETURN QUERY
  SELECT 
    d.id,
    d.content,
    d.filename,
    d.file_type,
    d.metadata,
    d.created_at,
    similarity(d.content, search_query) as similarity_score,
    'keyword'::text as search_type
  FROM documents d
  WHERE d.content ILIKE '%' || search_query || '%'
    AND d.id NOT IN (
      SELECT doc.id FROM documents doc
      WHERE doc.embedding IS NOT NULL
        AND 1 - (doc.embedding <=> query_embedding) > similarity_threshold
      ORDER BY doc.embedding <=> query_embedding
      LIMIT match_limit / 2
    )
  ORDER BY similarity(d.content, search_query) DESC
  LIMIT match_limit / 2;
END;
$$ LANGUAGE plpgsql;

-- 6. 創建批次向量化函數 (用於現有文件)
CREATE OR REPLACE FUNCTION get_documents_without_embeddings(batch_size int DEFAULT 10)
RETURNS TABLE(
  id integer,
  content text,
  filename varchar(255)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.content,
    d.filename
  FROM documents d
  WHERE d.embedding IS NULL
    AND d.content IS NOT NULL
    AND length(d.content) > 10
  ORDER BY d.created_at DESC
  LIMIT batch_size;
END;
$$ LANGUAGE plpgsql;

-- 7. 更新文件向量的函數
CREATE OR REPLACE FUNCTION update_document_embedding(
  doc_id integer,
  doc_embedding vector(1536)
)
RETURNS boolean AS $$
BEGIN
  UPDATE documents
  SET embedding = doc_embedding
  WHERE id = doc_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 8. 添加向量搜尋統計視圖
CREATE OR REPLACE VIEW vector_search_stats AS
SELECT 
  COUNT(*) as total_documents,
  COUNT(*) FILTER (WHERE embedding IS NOT NULL) as vectorized_documents,
  COUNT(*) FILTER (WHERE embedding IS NULL) as pending_vectorization,
  ROUND(
    (COUNT(*) FILTER (WHERE embedding IS NOT NULL)::numeric / COUNT(*)) * 100, 2
  ) as vectorization_percentage
FROM documents;

-- 9. 創建索引管理函數 (使用 HNSW)
CREATE OR REPLACE FUNCTION rebuild_vector_index()
RETURNS text AS $$
BEGIN
  -- 重建向量索引 (使用 HNSW)
  DROP INDEX IF EXISTS idx_documents_embedding_hnsw;
  CREATE INDEX idx_documents_embedding_hnsw
  ON documents USING hnsw (embedding vector_cosine_ops);
  
  RETURN 'HNSW vector index rebuilt successfully';
END;
$$ LANGUAGE plpgsql;

-- 10. 設定 RLS 政策更新 (包含向量欄位)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow anonymous access' AND tablename = 'documents') THEN
    ALTER POLICY "Allow anonymous access" ON documents USING (true);
  END IF;
  
  IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Allow authenticated access' AND tablename = 'documents') THEN
    ALTER POLICY "Allow authenticated access" ON documents USING (true);
  END IF;
END
$$;

-- 11. 加入註釋
COMMENT ON COLUMN documents.embedding IS 'OpenAI text-embedding-3-small vector (1536 dimensions) - Supabase compatible';
COMMENT ON FUNCTION search_documents_by_vector IS 'Semantic search using vector similarity (1536-dim)';
COMMENT ON FUNCTION hybrid_search_documents IS 'Combined keyword and vector search (1536-dim)';
COMMENT ON VIEW vector_search_stats IS 'Statistics about document vectorization status';

-- 12. 清理舊的 IVFFlat 索引 (如果存在)
DROP INDEX IF EXISTS idx_documents_embedding;

-- 驗證設定
SELECT 
  'Vector RAG setup completed successfully with text-embedding-3-small (1536-dim)!' as status,
  'HNSW index created for optimal performance' as index_type;