# n8n MCP 配置環境變數

# 基本 n8n 配置
N8N_HOST=0.0.0.0
N8N_PORT=5678
WEBHOOK_URL=http://localhost:5678/

# MCP 啟用設定（必要）
N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true

# API 金鑰配置
GOOGLE_GEMINI_API_KEY=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# MCP 伺服器配置
MCP_SERVER_URL=http://localhost:3001
N8N_API_URL=http://localhost:5678/api/v1
N8N_API_KEY=your_n8n_api_key_here

# MCP 外部服務 API（選擇性）
MCP_BRAVE_API_KEY=your-brave-api-key
MCP_OPENAI_API_KEY=your-openai-key
MCP_SERPER_API_KEY=your-serper-key
MCP_WEATHER_API_KEY=your-weather-api-key

# Webhook 認證
N8N_WEBHOOK_USERNAME=admin
N8N_WEBHOOK_PASSWORD=secure_password